using ShellProgressBar;
using Aspose.Words;
using WordProcessorLib;
using WordProcessorLib.Interfaces;
using WordProcessorLib.PipelineSteps;
using WordProcessorLib.Services;
using WordProcessorLib.Utilities;
using System.Drawing;
// 既要用using，还要右键项目添加引用（或者在csproj文件中引用）

// =============================================== 使用方法 ===============================================
// 1.根据文档类型决定是否注释 RemoveQuestionTypeStep。试卷不注释，讲义另行确定，其余注释
// 2.执行批次 1
// 3.将所有文档全选将字体改为 "XITS Math"，将标题换行并删除难度标记
// 4.执行批次 2
// 5.如需制作学生版，则执行批次 3
// =======================================================================================================

// ======================================= 配置需要处理的目标文件夹路径 =======================================
// 可使用@前缀处理路径中的特殊字符
const string sourceFolderPath = "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/教学/初中/初中物理/【题库】初中物理/【题库】初中物理  同步/【讲义】初中物理  同步讲义（旧教材2025版）（正确END整理中）";

// =================================================== 配置替换字典 ===================================================
// Key为查找内容，Value为替换内容
var replaceDict = new Dictionary<string, string>
{
    ["•"] = "⋅", ["∙"] = "⋅", ["∥"] = "//", ["｡"] = "。", ["＝"] = "=", ["ϕ"] = "φ", ["·"] = "⋅", ["【1】"] = "【D1】", ["（多选题）"] = "", ["（多选）"] = "", ["(多选)"] = "",
    ["||"] = "//",
    ["考点、"] = "【考点】", ["考点："] = "【考点】", ["考点:"] = "【考点】",
    ["【名师点睛】"] = "【点睛】", ["【方法点睛】"] = "【点睛】", ["点睛："] = "【点睛】", ["点睛:"] = "【点睛】", ["【思路点睛】"] = "【点睛】", ["【思维点睛】"] = "【点睛】",
    ["课前练习"] = "概念辨析",
    // ["巩固篇"] = "提升篇", ["梳脉理络"] = "考点攻略", ["培优卷"] = "提升卷",
    // ["知识讲解"] = "考点攻略", ["专项练习"] = "精思巧练", ["举一反三："] = "",
    // ["梳脉理络"] = "概念辨析", ["课堂例题"] = "考点攻略", ["【思考】"] = "",
    
    // ["【变式】"] = "", ["【变式1】"] = "", ["【变式2】"] = "", ["【变式3】"] = "", ["【变式4】"] = "", ["【变式5】"] = "", ["【变式6】"] = "", ["【变式7】"] = "", ["【变式8】"] = "", ["【变式9】"] = "", ["【变式10】"] = "",
    // ["【变式11】"] = "", ["【变式12】"] = "", ["【变式13】"] = "", ["【变式14】"] = "", ["【变式15】"] = "", ["【变式16】"] = "", ["【变式17】"] = "", ["【变式18】"] = "", ["【变式19】"] = "", ["【变式20】"] = "",
    // ["【变式21】"] = "", ["【变式22】"] = "", ["【变式23】"] = "", ["【变式24】"] = "", ["【变式25】"] = "", ["【变式26】"] = "", ["【变式27】"] = "", ["【变式28】"] = "", ["【变式29】"] = "", ["【变式30】"] = "",
    // ["【变式31】"] = "", ["【变式32】"] = "", ["【变式33】"] = "", ["【变式34】"] = "", ["【变式35】"] = "", ["【变式36】"] = "", ["【变式37】"] = "", ["【变式38】"] = "", ["【变式39】"] = "", ["【变式40】"] = "",
    // ["【变式41】"] = "", ["【变式42】"] = "", ["【变式43】"] = "", ["【变式44】"] = "", ["【变式45】"] = "", ["【变式46】"] = "", ["【变式47】"] = "", ["【变式48】"] = "", ["【变式49】"] = "", ["【变式50】"] = "",
    // ["【变式51】"] = "", ["【变式52】"] = "", ["【变式53】"] = "", ["【变式54】"] = "", ["【变式55】"] = "", ["【变式56】"] = "", ["【变式57】"] = "", ["【变式58】"] = "", ["【变式59】"] = "", ["【变式60】"] = "",
    // ["【变式61】"] = "", ["【变式62】"] = "", ["【变式63】"] = "", ["【变式64】"] = "", ["【变式65】"] = "", ["【变式66】"] = "", ["【变式67】"] = "", ["【变式68】"] = "", ["【变式69】"] = "", ["【变式70】"] = "",
    // ["【变式71】"] = "", ["【变式72】"] = "", ["【变式73】"] = "", ["【变式74】"] = "", ["【变式75】"] = "", ["【变式76】"] = "", ["【变式77】"] = "", ["【变式78】"] = "", ["【变式79】"] = "", ["【变式80】"] = "",
    // ["【变式81】"] = "", ["【变式82】"] = "", ["【变式83】"] = "", ["【变式84】"] = "", ["【变式85】"] = "", ["【变式86】"] = "", ["【变式87】"] = "", ["【变式88】"] = "", ["【变式89】"] = "", ["【变式90】"] = "",
    // ["【变式91】"] = "", ["【变式92】"] = "", ["【变式93】"] = "", ["【变式94】"] = "", ["【变式95】"] = "", ["【变式96】"] = "", ["【变式97】"] = "", ["【变式98】"] = "", ["【变式99】"] = "", ["【变式100】"] = "",
    // ["例题1（易）"] = "", ["例题2（易）"] = "", ["例题3（易）"] = "", ["例题4（易）"] = "", ["例题5（易）"] = "", ["例题6（易）"] = "", ["例题7（易）"] = "", ["例题8（易）"] = "", ["例题9（易）"] = "", ["例题10（易）"] = "",
    // ["例题11（易）"] = "", ["例题12（易）"] = "", ["例题13（易）"] = "", ["例题14（易）"] = "", ["例题15（易）"] = "", ["例题16（易）"] = "", ["例题17（易）"] = "", ["例题18（易）"] = "", ["例题19（易）"] = "", ["例题20（易）"] = "",
    // ["例题21（易）"] = "", ["例题22（易）"] = "", ["例题23（易）"] = "", ["例题24（易）"] = "", ["例题25（易）"] = "", ["例题26（易）"] = "", ["例题27（易）"] = "", ["例题28（易）"] = "", ["例题29（易）"] = "", ["例题30（易）"] = "",
    // ["例题31（易）"] = "", ["例题32（易）"] = "", ["例题33（易）"] = "", ["例题34（易）"] = "", ["例题35（易）"] = "", ["例题36（易）"] = "", ["例题37（易）"] = "", ["例题38（易）"] = "", ["例题39（易）"] = "", ["例题40（易）"] = "",
    // ["例题41（易）"] = "", ["例题42（易）"] = "", ["例题43（易）"] = "", ["例题44（易）"] = "", ["例题45（易）"] = "", ["例题46（易）"] = "", ["例题47（易）"] = "", ["例题48（易）"] = "", ["例题49（易）"] = "", ["例题50（易）"] = "",
    // ["例题51（易）"] = "", ["例题52（易）"] = "", ["例题53（易）"] = "", ["例题54（易）"] = "", ["例题55（易）"] = "", ["例题56（易）"] = "", ["例题57（易）"] = "", ["例题58（易）"] = "", ["例题59（易）"] = "", ["例题60（易）"] = "",
    // ["例题61（易）"] = "", ["例题62（易）"] = "", ["例题63（易）"] = "", ["例题64（易）"] = "", ["例题65（易）"] = "", ["例题66（易）"] = "", ["例题67（易）"] = "", ["例题68（易）"] = "", ["例题69（易）"] = "", ["例题70（易）"] = "",
    // ["例题71（易）"] = "", ["例题72（易）"] = "", ["例题73（易）"] = "", ["例题74（易）"] = "", ["例题75（易）"] = "", ["例题76（易）"] = "", ["例题77（易）"] = "", ["例题78（易）"] = "", ["例题79（易）"] = "", ["例题80（易）"] = "",
    // ["例题81（易）"] = "", ["例题82（易）"] = "", ["例题83（易）"] = "", ["例题84（易）"] = "", ["例题85（易）"] = "", ["例题86（易）"] = "", ["例题87（易）"] = "", ["例题88（易）"] = "", ["例题89（易）"] = "", ["例题90（易）"] = "",
    // ["例题91（易）"] = "", ["例题92（易）"] = "", ["例题93（易）"] = "", ["例题94（易）"] = "", ["例题95（易）"] = "", ["例题96（易）"] = "", ["例题97（易）"] = "", ["例题98（易）"] = "", ["例题99（易）"] = "", ["例题100（易）"] = "",
    // ["例题1（中）"] = "", ["例题2（中）"] = "", ["例题3（中）"] = "", ["例题4（中）"] = "", ["例题5（中）"] = "", ["例题6（中）"] = "", ["例题7（中）"] = "", ["例题8（中）"] = "", ["例题9（中）"] = "", ["例题10（中）"] = "",
    // ["例题11（中）"] = "", ["例题12（中）"] = "", ["例题13（中）"] = "", ["例题14（中）"] = "", ["例题15（中）"] = "", ["例题16（中）"] = "", ["例题17（中）"] = "", ["例题18（中）"] = "", ["例题19（中）"] = "", ["例题20（中）"] = "",
    // ["例题21（中）"] = "", ["例题22（中）"] = "", ["例题23（中）"] = "", ["例题24（中）"] = "", ["例题25（中）"] = "", ["例题26（中）"] = "", ["例题27（中）"] = "", ["例题28（中）"] = "", ["例题29（中）"] = "", ["例题30（中）"] = "",
    // ["例题31（中）"] = "", ["例题32（中）"] = "", ["例题33（中）"] = "", ["例题34（中）"] = "", ["例题35（中）"] = "", ["例题36（中）"] = "", ["例题37（中）"] = "", ["例题38（中）"] = "", ["例题39（中）"] = "", ["例题40（中）"] = "",
    // ["例题41（中）"] = "", ["例题42（中）"] = "", ["例题43（中）"] = "", ["例题44（中）"] = "", ["例题45（中）"] = "", ["例题46（中）"] = "", ["例题47（中）"] = "", ["例题48（中）"] = "", ["例题49（中）"] = "", ["例题50（中）"] = "",
    // ["例题51（中）"] = "", ["例题52（中）"] = "", ["例题53（中）"] = "", ["例题54（中）"] = "", ["例题55（中）"] = "", ["例题56（中）"] = "", ["例题57（中）"] = "", ["例题58（中）"] = "", ["例题59（中）"] = "", ["例题60（中）"] = "",
    // ["例题61（中）"] = "", ["例题62（中）"] = "", ["例题63（中）"] = "", ["例题64（中）"] = "", ["例题65（中）"] = "", ["例题66（中）"] = "", ["例题67（中）"] = "", ["例题68（中）"] = "", ["例题69（中）"] = "", ["例题70（中）"] = "",
    // ["例题71（中）"] = "", ["例题72（中）"] = "", ["例题73（中）"] = "", ["例题74（中）"] = "", ["例题75（中）"] = "", ["例题76（中）"] = "", ["例题77（中）"] = "", ["例题78（中）"] = "", ["例题79（中）"] = "", ["例题80（中）"] = "",
    // ["例题81（中）"] = "", ["例题82（中）"] = "", ["例题83（中）"] = "", ["例题84（中）"] = "", ["例题85（中）"] = "", ["例题86（中）"] = "", ["例题87（中）"] = "", ["例题88（中）"] = "", ["例题89（中）"] = "", ["例题90（中）"] = "",
    // ["例题91（中）"] = "", ["例题92（中）"] = "", ["例题93（中）"] = "", ["例题94（中）"] = "", ["例题95（中）"] = "", ["例题96（中）"] = "", ["例题97（中）"] = "", ["例题98（中）"] = "", ["例题99（中）"] = "", ["例题100（中）"] = "",
    // ["例题1（难）"] = "", ["例题2（难）"] = "", ["例题3（难）"] = "", ["例题4（难）"] = "", ["例题5（难）"] = "", ["例题6（难）"] = "", ["例题7（难）"] = "", ["例题8（难）"] = "", ["例题9（难）"] = "", ["例题10（难）"] = "",
    // ["例题11（难）"] = "", ["例题12（难）"] = "", ["例题13（难）"] = "", ["例题14（难）"] = "", ["例题15（难）"] = "", ["例题16（难）"] = "", ["例题17（难）"] = "", ["例题18（难）"] = "", ["例题19（难）"] = "", ["例题20（难）"] = "",
    // ["例题21（难）"] = "", ["例题22（难）"] = "", ["例题23（难）"] = "", ["例题24（难）"] = "", ["例题25（难）"] = "", ["例题26（难）"] = "", ["例题27（难）"] = "", ["例题28（难）"] = "", ["例题29（难）"] = "", ["例题30（难）"] = "",
    // ["例题31（难）"] = "", ["例题32（难）"] = "", ["例题33（难）"] = "", ["例题34（难）"] = "", ["例题35（难）"] = "", ["例题36（难）"] = "", ["例题37（难）"] = "", ["例题38（难）"] = "", ["例题39（难）"] = "", ["例题40（难）"] = "",
    // ["例题41（难）"] = "", ["例题42（难）"] = "", ["例题43（难）"] = "", ["例题44（难）"] = "", ["例题45（难）"] = "", ["例题46（难）"] = "", ["例题47（难）"] = "", ["例题48（难）"] = "", ["例题49（难）"] = "", ["例题50（难）"] = "",
    // ["例题51（难）"] = "", ["例题52（难）"] = "", ["例题53（难）"] = "", ["例题54（难）"] = "", ["例题55（难）"] = "", ["例题56（难）"] = "", ["例题57（难）"] = "", ["例题58（难）"] = "", ["例题59（难）"] = "", ["例题60（难）"] = "",
    // ["例题61（难）"] = "", ["例题62（难）"] = "", ["例题63（难）"] = "", ["例题64（难）"] = "", ["例题65（难）"] = "", ["例题66（难）"] = "", ["例题67（难）"] = "", ["例题68（难）"] = "", ["例题69（难）"] = "", ["例题70（难）"] = "",
    // ["例题71（难）"] = "", ["例题72（难）"] = "", ["例题73（难）"] = "", ["例题74（难）"] = "", ["例题75（难）"] = "", ["例题76（难）"] = "", ["例题77（难）"] = "", ["例题78（难）"] = "", ["例题79（难）"] = "", ["例题80（难）"] = "",
    // ["例题81（难）"] = "", ["例题82（难）"] = "", ["例题83（难）"] = "", ["例题84（难）"] = "", ["例题85（难）"] = "", ["例题86（难）"] = "", ["例题87（难）"] = "", ["例题88（难）"] = "", ["例题89（难）"] = "", ["例题90（难）"] = "",
    // ["例题91（难）"] = "", ["例题92（难）"] = "", ["例题93（难）"] = "", ["例题94（难）"] = "", ["例题95（难）"] = "", ["例题96（难）"] = "", ["例题97（难）"] = "", ["例题98（难）"] = "", ["例题99（难）"] = "", ["例题100（难）"] = "",
    // ["【例1】"] = "", ["【例2】"] = "", ["【例3】"] = "", ["【例4】"] = "", ["【例5】"] = "", ["【例6】"] = "", ["【例7】"] = "", ["【例8】"] = "", ["【例9】"] = "", ["【例10】"] = "",
    // ["【例11】"] = "", ["【例12】"] = "", ["【例13】"] = "", ["【例14】"] = "", ["【例15】"] = "", ["【例16】"] = "", ["【例17】"] = "", ["【例18】"] = "", ["【例19】"] = "", ["【例20】"] = "",
    // ["【例21】"] = "", ["【例22】"] = "", ["【例23】"] = "", ["【例24】"] = "", ["【例25】"] = "", ["【例26】"] = "", ["【例27】"] = "", ["【例28】"] = "", ["【例29】"] = "", ["【例30】"] = "",
    // ["【例31】"] = "", ["【例32】"] = "", ["【例33】"] = "", ["【例34】"] = "", ["【例35】"] = "", ["【例36】"] = "", ["【例37】"] = "", ["【例38】"] = "", ["【例39】"] = "", ["【例40】"] = "",
    // ["【例41】"] = "", ["【例42】"] = "", ["【例43】"] = "", ["【例44】"] = "", ["【例45】"] = "", ["【例46】"] = "", ["【例47】"] = "", ["【例48】"] = "", ["【例49】"] = "", ["【例50】"] = "",
    // ["【例51】"] = "", ["【例52】"] = "", ["【例53】"] = "", ["【例54】"] = "", ["【例55】"] = "", ["【例56】"] = "", ["【例57】"] = "", ["【例58】"] = "", ["【例59】"] = "", ["【例60】"] = "",
    // ["【例61】"] = "", ["【例62】"] = "", ["【例63】"] = "", ["【例64】"] = "", ["【例65】"] = "", ["【例66】"] = "", ["【例67】"] = "", ["【例68】"] = "", ["【例69】"] = "", ["【例70】"] = "",
    // ["【例71】"] = "", ["【例72】"] = "", ["【例73】"] = "", ["【例74】"] = "", ["【例75】"] = "", ["【例76】"] = "", ["【例77】"] = "", ["【例78】"] = "", ["【例79】"] = "", ["【例80】"] = "",
    // ["【例81】"] = "", ["【例82】"] = "", ["【例83】"] = "", ["【例84】"] = "", ["【例85】"] = "", ["【例86】"] = "", ["【例87】"] = "", ["【例88】"] = "", ["【例89】"] = "", ["【例90】"] = "",
    // ["【例91】"] = "", ["【例92】"] = "", ["【例93】"] = "", ["【例94】"] = "", ["【例95】"] = "", ["【例96】"] = "", ["【例97】"] = "", ["【例98】"] = "", ["【例99】"] = "", ["【例100】"] = "",
    // ["例题1"] = "", ["例题2"] = "", ["例题3"] = "", ["例题4"] = "", ["例题5"] = "", ["例题6"] = "", ["例题7"] = "", ["例题8"] = "", ["例题9"] = "", ["例题10"] = "",
    // ["例题11"] = "", ["例题12"] = "", ["例题13"] = "", ["例题14"] = "", ["例题15"] = "", ["例题16"] = "", ["例题17"] = "", ["例题18"] = "", ["例题19"] = "", ["例题20"] = "",
    // ["例题21"] = "", ["例题22"] = "", ["例题23"] = "", ["例题24"] = "", ["例题25"] = "", ["例题26"] = "", ["例题27"] = "", ["例题28"] = "", ["例题29"] = "", ["例题30"] = "",
    // ["例题31"] = "", ["例题32"] = "", ["例题33"] = "", ["例题34"] = "", ["例题35"] = "", ["例题36"] = "", ["例题37"] = "", ["例题38"] = "", ["例题39"] = "", ["例题40"] = "",
    // ["例题41"] = "", ["例题42"] = "", ["例题43"] = "", ["例题44"] = "", ["例题45"] = "", ["例题46"] = "", ["例题47"] = "", ["例题48"] = "", ["例题49"] = "", ["例题50"] = "",
    // ["例题51"] = "", ["例题52"] = "", ["例题53"] = "", ["例题54"] = "", ["例题55"] = "", ["例题56"] = "", ["例题57"] = "", ["例题58"] = "", ["例题59"] = "", ["例题60"] = "",
    // ["例题61"] = "", ["例题62"] = "", ["例题63"] = "", ["例题64"] = "", ["例题65"] = "", ["例题66"] = "", ["例题67"] = "", ["例题68"] = "", ["例题69"] = "", ["例题70"] = "",
    // ["例题71"] = "", ["例题72"] = "", ["例题73"] = "", ["例题74"] = "", ["例题75"] = "", ["例题76"] = "", ["例题77"] = "", ["例题78"] = "", ["例题79"] = "", ["例题80"] = "",
    // ["例题81"] = "", ["例题82"] = "", ["例题83"] = "", ["例题84"] = "", ["例题85"] = "", ["例题86"] = "", ["例题87"] = "", ["例题88"] = "", ["例题89"] = "", ["例题90"] = "",
    // ["例题91"] = "", ["例题92"] = "", ["例题93"] = "", ["例题94"] = "", ["例题95"] = "", ["例题96"] = "", ["例题97"] = "", ["例题98"] = "", ["例题99"] = "", ["例题100"] = "",
};

// =================================================== 配置替换字典2 ===================================================
// Key为查找内容，Value为替换内容
var replaceDict2 = new Dictionary<string, string>
{
    // ["单元测试"] = "综合测试",
    ["例题 1"] = "", ["例题 2"] = "", ["例题 3"] = "", ["例题 4"] = "", ["例题 5"] = "", ["例题 6"] = "", ["例题 7"] = "", ["例题 8"] = "", ["例题 9"] = "", ["例题 10"] = "",
    ["例题 11"] = "", ["例题 12"] = "", ["例题 13"] = "", ["例题 14"] = "", ["例题 15"] = "", ["例题 16"] = "", ["例题 17"] = "", ["例题 18"] = "", ["例题 19"] = "", ["例题 20"] = "",
    ["例题 21"] = "", ["例题 22"] = "", ["例题 23"] = "", ["例题 24"] = "", ["例题 25"] = "", ["例题 26"] = "", ["例题 27"] = "", ["例题 28"] = "", ["例题 29"] = "", ["例题 30"] = "",
    ["例题 31"] = "", ["例题 32"] = "", ["例题 33"] = "", ["例题 34"] = "", ["例题 35"] = "", ["例题 36"] = "", ["例题 37"] = "", ["例题 38"] = "", ["例题 39"] = "", ["例题 40"] = "",
    ["例题 41"] = "", ["例题 42"] = "", ["例题 43"] = "", ["例题 44"] = "", ["例题 45"] = "", ["例题 46"] = "", ["例题 47"] = "", ["例题 48"] = "", ["例题 49"] = "", ["例题 50"] = "",
    ["例题 51"] = "", ["例题 52"] = "", ["例题 53"] = "", ["例题 54"] = "", ["例题 55"] = "", ["例题 56"] = "", ["例题 57"] = "", ["例题 58"] = "", ["例题 59"] = "", ["例题 60"] = "",
    ["例题 61"] = "", ["例题 62"] = "", ["例题 63"] = "", ["例题 64"] = "", ["例题 65"] = "", ["例题 66"] = "", ["例题 67"] = "", ["例题 68"] = "", ["例题 69"] = "", ["例题 70"] = "",
};

var replaceDict3 = new Dictionary<string, string>
{
    ["梳脉理络"] = "考点攻略",
};

// ======================================== 分别配置中西文字体和统一配置字号 =======================================
// 字体或字号值为null时则不改变
var fontSettings = new Dictionary<string, string?>
{
    ["latinFont"] = "XITS Math",     // 配置全局西文字体
    ["cjkFont"]   = "宋体",           // 配置全局中文字体
    ["fontSize"]  = "五号"             // 配置全局字号，尽量用 null，因为无法更改公式隔断符字体字号，仅在文档无公式时谨慎使用
};

// ============================== 配置公式中的中西文字体和字号 ==============================
// 字号值为 null 时则不改变大小；因为要分拆公式中的 Run，所以公式字体不能为 null
var mathFontSettings = new Dictionary<string, string?>
{
    ["mathLatinFont"]     = "XITS Math",     // 配置公式中的西文字体，不能为 null
    ["mathLatinFontSize"] = null,            // 配置公式中的西文字号，一般为 null，不改变大小
    ["mathCjkFont"]       = "宋体",           // 配置公式中的中文字体，不能为 null
    ["mathCjkFontSize"]   = null             // 配置公式中的西文字号，一般为 null，不改变大小
};

// ========================================= 配置插入的文件名中需要删除的部分 =========================================
// 是否删除的布尔值直接写在构造函数内，true启用删除，false跳过
List<string> fieldsToRemove = new List<string>
{
    "  基础", "  提升", "  培优",                  // 按需选择是否注释！
    "  [U]", "  []", "  [1]", "  [2]", "  [3]", "  [4]", "  [5]", "  [6]", "  [7]", "  [8]", "  [9]", "  [10]",
    "  [11]", "  [12]", "  [13]", "  [14]", "  [15]", "  [16]", "  [17]", "  [18]", "  [19]", "  [20]",
    "  [21]", "  [22]", "  [23]", "  [24]", "  [25]", "  [26]", "  [27]", "  [28]", "  [29]", "  [30]",
    "  [31]", "  [32]", "  [33]", "  [34]", "  [35]", "  [36]", "  [37]", "  [38]", "  [39]", "  [40]",
    "  [41]", "  [42]", "  [43]", "  [44]", "  [45]", "  [46]", "  [47]", "  [48]", "  [49]", "  [50]",
    "  [51]", "  [52]", "  [53]", "  [54]", "  [55]", "  [56]", "  [57]", "  [58]", "  [59]", "  [60]",
    "  [61]", "  [62]", "  [63]", "  [64]", "  [65]", "  [66]", "  [67]", "  [68]", "  [69]", "  [70]",
    "  [71]", "  [72]", "  [73]", "  [74]", "  [75]", "  [76]", "  [77]", "  [78]", "  [79]", "  [80]",
    "  [81]", "  [82]", "  [83]", "  [84]", "  [85]", "  [86]", "  [87]", "  [88]", "  [89]", "  [90]",
    "  [91]", "  [92]", "  [93]", "  [94]", "  [95]", "  [96]", "  [97]", "  [98]", "  [99]", "  [100]",
    "  [D0]", "  [D1]", "  [D2]", "  [D3]", "  [D4]", "  [D5]", "  [D6]", "  [D7]", "  [D8]", "  [D9]",
    "  [D10]", "  [D11]", "  [D12]", "  [D13]", "  [D14]", "  [D15]", "  [D16]", "  [D17]", "  [D18]", "  [D19]",
};

// =================================== 配置需要设置为非斜体的各学科函数 ===================================
// 通用函数名列表（区分大小写，严格匹配）
var mathFunctions = new List<string>
{
    "sin", "cos", "tan", "csc", "sec", "cot", "arcsin", "arccos",
    "arctan", "arccsc", "arcsec", "arccot", "sinh", "cosh", "tanh", "coth",
    "arcsinh", "arccosh", "arctanh", "arccoth", "log", "ln", "lg", "exp",
    "sqrt", "abs", "floor", "ceil", "round", "trunc", "sign", "deg",
    "rad", "grad", "cotd", "cscd", "secd", "coth", "arccotd", "arccscd",
    "max", "min", "lim", "lg", "∁", "∩", "∪", "∀",
    "∃", "∅", "∇", "∂", "∫", "∮", "∬", "∭", "π",
    "∰", "∱", "∂", "∳", "∵", "∴",
    "=", "+", "-", "×", "÷", "≤", "≥", "≠",
    "≈", "≌", ">", "<", "⇒", "⇔", "⇐", "⇑",
    "⇓", "∣", "∈", "∉", "∋", "∌", "⊆", "⊂",
    "⊄", "⊇", "⊃", "⊅", "∞", "∠", "kg", "m/s",
    "Pa", "Hz", "mol", "km/h", "π", "∑", "(", ")",
    "°", "△", "Δ", "sum", "（", "）", "：", ":",
    "，", "。", "！", "？", "、", "：", "；", "，",
    "。", "-", "?", "!", ",", ".", "⋅", "…", "%", "‰", "°C", "°F", "°K", "°R", "℃",
    "①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧", "⑨", "⑩",
    "kW•h", "kW∙h", "kW⋅h", "kW·h", "m•s", "m∙s", "m⋅s", "m·s", "km•h", "km∙h", "km⋅h", "km·h",
    "kg•m", "kg∙m", "kg⋅m", "kg·m", "g•cm", "g∙cm", "g⋅cm", "g·cm", "N•m", "N∙m", "N⋅m", "N·m",
    "J•kg", "J∙kg", "J⋅kg", "J·kg", "J•s", "J∙s", "J⋅s", "J·s", "rad•s", "rad∙s", "rad⋅s", "rad·s",
    "g•mol", "g∙mol", "g⋅mol", "g·mol", "J•g", "J∙g", "J⋅g", "J·g",
    "kg/(m•s", "kg/(m∙s", "kg/(m⋅s", "kg/(m·s", "kg•m/s", "kg∙m/s", "kg⋅m/s", "kg·m/s",
    "mL", "J/(kg⋅℃)", "J/(kg", "J/（kg", "J/（kg⋅℃）", "kg/m", "g/cm",  "g/dm",  "g/m", "N/m",
    "C/s", "J/s", "J/(g⋅℃)", "J/kg", "J/g", "J/m", "J/cm", "J/C", "V/A",
    "L/s", "L/min", "L/h",
    "mm", "cm", "dm", "km", "nm", "μm", "pH",
    "min", "week", "month", "year",
    "mA", "μA", "mV", "kV", "Ω", "mΩ", "kΩ", "MΩ", "kW", "MW", "mW", "kW·h",
    "Pa", "kPa", "MPa", "atm", "mmHg", "cmHg",
    "kJ", "eV", "keV", "MeV", "GeV", "TeV", "meV", "μeV", "neV",
    "cal", "kcal", "Mcal", "mcal", "kWh", "MWh", "GWh", "mWh",
    "Hz", "kHz", "MHz", "GHz", "mHz", "rpm", "rps",
    "km/s", "km/min", "km/h", "m/s", "m/min", "m/h", "cm/s", "mm/s", "μm/s", "nm/s", "mph", "km/s",
    "m/s²", "cm/s²", "mm/s²", "km/s²", "μm/s²", "nm/s²",
    "mol", "g/mol", "kg/mol", "mg/mol",
    "rad", "deg", "grad", "arcmin", "arcsec", "rad/s", "rad/min", "rad/h",
    "g/cm", "g/dm", "g/m", "kg/cm", "kg/dm", "kg/m", "mg/cm", "μg/cm", "g/mL", "mg/mL", "μg/mL", "g/L", "kg/L",
    "ppm", "mg/L", "μg/L", "ng/L", "g/L", "kg/L", "mg/dL",
};

// 初中物理专用函数名列表（区分大小写，严格匹配）
var physicsFunctions = new List<string>
{
    "A", "C", "J", "N",
};

// 初中化学专用函数名列表（区分大小写，严格匹配）
var chemistryFunctions = new List<string>
{
    "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
    "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", 
    "Na", "Mg", "Al", "Si", "Cl", "Ca", "Fe", "Cu", "Zn", "Ag", "Au", "Hg", "Pb", "Bi", "Cl", "Ca", "Fe", "Cu", "Zn", "Ag", 
    "Au", "Hg", "Pb", "Bi", "CO",
};

// ====================== 配置需要设置为非斜体的微积分符号 ======================
// 实际的微积分符号名列表（区分大小写，严格匹配）
// 注意：只有 d 字符会被设置为非斜体，后面的变量字母保持原有格式
var calculusSymbols = new List<string>
{
    "dx", "dy", "df", "dt", "du", "dv", "dw", "dz",
    "dr", "dθ", "dφ", "dα", "dβ", "dγ", "dδ", "dε",
    "dη", "dλ", "dμ", "dν", "dξ", "dπ", "dρ", "dσ",
    "dτ", "dυ", "dχ", "dψ", "dω", "dΩ"
};

// ====================== 配置需要设置前置空行的字段和空行数量 ======================
// 支持多个标识符同时匹配
var markersToAdjust = new List<string>
{
    "【答案】",
};
const int spacesToAdjust = 8;                   // 需要设置的空行数，以 8 行为标准

// ====================== 配置段落缩进字符和缩进值 ======================
// 需要设置缩进的段落开头字符列表
var indentCharacters = new List<string>
{
    "A．", "B．", "C．", "D．", "E．", "F．", "G．", "H．", "I．", "J．", "K．", "L．", "M．", "N．"
};

// ====================== 配置题号跳过字段列表 ======================
// 这些字段所在段落与题号要连在一起，中间不能有空行
var questionSkipFields = new List<string>
{
    "【变式】", "【变式1】", "【变式2】", "【变式3】", "【变式4】", "【变式5】", "【变式6】", "【变式7】", "【变式8】", "【变式9】", "【变式10】",
    "【变式11】", "【变式12】", "【变式13】", "【变式14】", "【变式15】", "【变式16】", "【变式17】", "【变式18】", "【变式19】", "【变式20】",
    "【变式21】", "【变式22】", "【变式23】", "【变式24】", "【变式25】", "【变式26】", "【变式27】", "【变式28】", "【变式29】", "【变式30】",
    "【变式31】", "【变式32】", "【变式33】", "【变式34】", "【变式35】", "【变式36】", "【变式37】", "【变式38】", "【变式39】", "【变式40】",
    "【变式41】", "【变式42】", "【变式43】", "【变式44】", "【变式45】", "【变式46】", "【变式47】", "【变式48】", "【变式49】", "【变式50】",
    "【例1】", "【例2】", "【例3】", "【例4】", "【例5】", "【例6】", "【例7】", "【例8】", "【例9】", "【例10】",
    "【例11】", "【例12】", "【例13】", "【例14】", "【例15】", "【例16】", "【例17】", "【例18】", "【例19】", "【例20】",
    "【例21】", "【例22】", "【例23】", "【例24】", "【例25】", "【例26】", "【例27】", "【例28】", "【例29】", "【例30】",
    "【例31】", "【例32】", "【例33】", "【例34】", "【例35】", "【例36】", "【例37】", "【例38】", "【例39】", "【例40】",
    "【例41】", "【例42】", "【例43】", "【例44】", "【例45】", "【例46】", "【例47】", "【例48】", "【例49】", "【例50】",
    "【例51】", "【例52】", "【例53】", "【例54】", "【例55】", "【例56】", "【例57】", "【例58】", "【例59】", "【例60】",
    "【例61】", "【例62】", "【例63】", "【例64】", "【例65】", "【例66】", "【例67】", "【例68】", "【例69】", "【例70】",
    "【例71】", "【例72】", "【例73】", "【例74】", "【例75】", "【例76】", "【例77】", "【例78】", "【例79】", "【例80】",
    "【例81】", "【例82】", "【例83】", "【例84】", "【例85】", "【例86】", "【例87】", "【例88】", "【例89】", "【例90】",
    "【例91】", "【例92】", "【例93】", "【例94】", "【例95】", "【例96】", "【例97】", "【例98】", "【例99】", "【例100】",
    "例题", "例题1", "例题2", "例题3", "例题4", "例题5", "例题6", "例题7", "例题8", "例题9", "例题10",
    "例题11", "例题12", "例题13", "例题14", "例题15", "例题16", "例题17", "例题18", "例题19", "例题20",
    "例题21", "例题22", "例题23", "例题24", "例题25", "例题26", "例题27", "例题28", "例题29", "例题30",
    "例题31", "例题32", "例题33", "例题34", "例题35", "例题36", "例题37", "例题38", "例题39", "例题40",
    "例题41", "例题42", "例题43", "例题44", "例题45", "例题46", "例题47", "例题48", "例题49", "例题50",
    "举一反三：", "举一反三", 
};

// ============================= 配置需要删除所在段落及上下空行的指定内容 =============================
// 配置目标字段列表，指定内容所在段落仅含指定内容文本，否则不会匹配
var targetFields = new List<string>
{
    "一、单选题", "二、单选题", "三、单选题", "四、单选题", "五、单选题", "六、单选题", "七、单选题", "八、单选题", "九、单选题", "十、单选题",
    "十一、单选题", "十二、单选题", "十三、单选题", "十四、单选题", "十五、单选题", "十六、单选题", "十七、单选题", "十八、单选题", "十九、单选题", "二十、单选题",
    "一、多选题", "二、多选题", "三、多选题", "四、多选题", "五、多选题", "六、多选题", "七、多选题", "八、多选题", "九、多选题", "十、多选题",
    "十一、多选题", "十二、多选题", "十三、多选题", "十四、多选题", "十五、多选题", "十六、多选题", "十七、多选题", "十八、多选题", "十九、多选题", "二十、多选题",
    "一、填空题", "二、填空题", "三、填空题", "四、填空题", "五、填空题", "六、填空题", "七、填空题", "八、填空题", "九、填空题", "十、填空题",
    "十一、填空题", "十二、填空题", "十三、填空题", "十四、填空题", "十五、填空题", "十六、填空题", "十七、填空题", "十八、填空题", "十九、填空题", "二十、填空题",
    "一、解答题", "二、解答题", "三、解答题", "四、解答题", "五、解答题", "六、解答题", "七、解答题", "八、解答题", "九、解答题", "十、解答题",
    "十一、解答题", "十二、解答题", "十三、解答题", "十四、解答题", "十五、解答题", "十六、解答题", "十七、解答题", "十八、解答题", "十九、解答题", "二十、解答题",
    "一、计算题", "二、计算题", "三、计算题", "四、计算题", "五、计算题", "六、计算题", "七、计算题", "八、计算题", "九、计算题", "十、计算题",
    "十一、计算题", "十二、计算题", "十三、计算题", "十四、计算题", "十五、计算题", "十六、计算题", "十七、计算题", "十八、计算题", "十九、计算题", "二十、计算题",
    "一、实验题", "二、实验题", "三、实验题", "四、实验题", "五、实验题", "六、实验题", "七、实验题", "八、实验题", "九、实验题", "十、实验题",
    "十一、实验题", "十二、实验题", "十三、实验题", "十四、实验题", "十五、实验题", "十六、实验题", "十七、实验题", "十八、实验题", "十九、实验题", "二十、实验题",
    "一、作图题", "二、作图题", "三、作图题", "四、作图题", "五、作图题", "六、作图题", "七、作图题", "八、作图题", "九、作图题", "十、作图题",
    "十一、作图题", "十二、作图题", "十三、作图题", "十四、作图题", "十五、作图题", "十六、作图题", "十七、作图题", "十八、作图题", "十九、作图题", "二十、作图题",
    "一、综合题", "二、综合题", "三、综合题", "四、综合题", "五、综合题", "六、综合题", "七、综合题", "八、综合题", "九、综合题", "十、综合题",
    "十一、综合题", "十二、综合题", "十三、综合题", "十四、综合题", "十五、综合题", "十六、综合题", "十七、综合题", "十八、综合题", "十九、综合题", "二十、综合题",
    "一、判断题", "二、判断题", "三、判断题", "四、判断题", "五、判断题", "六、判断题", "七、判断题", "八、判断题", "九、判断题", "十、判断题",
    "十一、判断题", "十二、判断题", "十三、判断题", "十四、判断题", "十五、判断题", "十六、判断题", "十七、判断题", "十八、判断题", "十九、判断题", "二十、判断题",
    "一、选择题", "二、选择题", "三、选择题", "四、选择题", "五、选择题", "六、选择题", "七、选择题", "八、选择题", "九、选择题", "十、选择题",
    "十一、选择题", "十二、选择题", "十三、选择题", "十四、选择题", "十五、选择题", "十六、选择题", "十七、选择题", "十八、选择题", "十九、选择题", "二十、选择题",
    "一、科普阅读题", "二、科普阅读题", "三、科普阅读题", "四、科普阅读题", "五、科普阅读题", "六、科普阅读题", "七、科普阅读题", "八、科普阅读题", "九、科普阅读题",
    "十一、科普阅读题", "十二、科普阅读题", "十三、科普阅读题", "十四、科普阅读题", "十五、科普阅读题", "十六、科普阅读题", "十七、科普阅读题", "十八、科普阅读题", "十九、科普阅读题", "二十、科普阅读题",
    "一、填空与简答题", "二、填空与简答题", "三、填空与简答题", "四、填空与简答题", "五、填空与简答题", "六、填空与简答题", "七、填空与简答题", "八、填空与简答题", "九、填空与简答题",
    "十、填空与简答题", "十一、填空与简答题", "十二、填空与简答题", "十三、填空与简答题", "十四、填空与简答题", "十五、填空与简答题", "十六、填空与简答题", "十七、填空与简答题", "十八、填空与简答题", "十九、填空与简答题", "二十、填空与简答题",
    "一、综合应用题", "二、综合应用题", "三、综合应用题", "四、综合应用题", "五、综合应用题", "六、综合应用题", "七、综合应用题", "八、综合应用题", "九、综合应用题",
    "十、综合应用题", "十一、综合应用题", "十二、综合应用题", "十三、综合应用题", "十四、综合应用题", "十五、综合应用题", "十六、综合应用题", "十七、综合应用题", "十八、综合应用题", "十九、综合应用题", "二十、综合应用题",
    "一、科学探究题", "二、科学探究题", "三、科学探究题", "四、科学探究题", "五、科学探究题", "六、科学探究题", "七、科学探究题", "八、科学探究题", "九、科学探究题",
    "十、科学探究题", "十一、科学探究题", "十二、科学探究题", "十三、科学探究题", "十四、科学探究题", "十五、科学探究题", "十六、科学探究题", "十七、科学探究题", "十八、科学探究题", "十九、科学探究题", "二十、科学探究题",
    "一、填空与简答", "二、填空与简答", "三、填空与简答", "四、填空与简答", "五、填空与简答", "六、填空与简答", "七、填空与简答", "八、填空与简答", "九、填空与简答",
    "十、填空与简答", "十一、填空与简答", "十二、填空与简答", "十三、填空与简答", "十四、填空与简答", "十五、填空与简答", "十六、填空与简答", "十七、填空与简答", "十八、填空与简答", "十九、填空与简答", "二十、填空与简答",
    "一、应用题", "二、应用题", "三、应用题", "四、应用题", "五、应用题", "六、应用题", "七、应用题", "八、应用题", "九、应用题", "十、应用题",
    "十一、应用题", "十二、应用题", "十三、应用题", "十四、应用题", "十五、应用题", "十六、应用题", "十七、应用题", "十八、应用题", "十九、应用题", "二十、应用题",
    "一、简答题", "二、简答题", "三、简答题", "四、简答题", "五、简答题", "六、简答题", "七、简答题", "八、简答题", "九、简答题", "十、简答题",
    "十一、简答题", "十二、简答题", "十三、简答题", "十四、简答题", "十五、简答题", "十六、简答题", "十七、简答题", "十八、简答题", "十九、简答题", "二十、简答题",
    "一、作图题", "二、作图题", "三、作图题", "四、作图题", "五、作图题", "六、作图题", "七、作图题", "八、作图题", "九、作图题", "十、作图题",
    "十一、作图题", "十二、作图题", "十三、作图题", "十四、作图题", "十五、作图题", "十六、作图题", "十七、作图题", "十八、作图题", "十九、作图题", "二十、作图题",
    // "练习小题", "考向",
    "考向1", "考向2", "考向3", "考向4", "考向5", "考向6", "考向7", "考向8", "考向9", "考向10",
    "考向11", "考向12", "考向13", "考向14", "考向15", "考向16", "考向17", "考向18", "考向19", "考向20",
    "考向21", "考向22", "考向23", "考向24", "考向25", "考向26", "考向27", "考向28", "考向29", "考向30",
    "考向31", "考向32", "考向33", "考向34", "考向35", "考向36", "考向37", "考向38", "考向39", "考向40",
    "考向41", "考向42", "考向43", "考向44", "考向45", "考向46", "考向47", "考向48", "考向49", "考向50",
    "知识点1", "知识点2", "知识点3", "知识点4", "知识点5", "知识点6", "知识点7", "知识点8", "知识点9", "知识点10",
    "知识点11", "知识点12", "知识点13", "知识点14", "知识点15", "知识点16", "知识点17", "知识点18", "知识点19", "知识点20",
    "知识点21", "知识点22", "知识点23", "知识点24", "知识点25", "知识点26", "知识点27", "知识点28", "知识点29", "知识点30",
    "知识点31", "知识点32", "知识点33", "知识点34", "知识点35", "知识点36", "知识点37", "知识点38", "知识点39", "知识点40",
    "知识点41", "知识点42", "知识点43", "知识点44", "知识点45", "知识点46", "知识点47", "知识点48", "知识点49", "知识点50",
    "类型一", "类型二", "类型三", "类型四", "类型五", "类型六", "类型七", "类型八", "类型九", "类型十",
    "类型十一", "类型十二", "类型十三", "类型十四", "类型十五", "类型十六", "类型十七", "类型十八", "类型十九", "类型二十",
    "类型二十一", "类型二十二", "类型二十三", "类型二十四", "类型二十五", "类型二十六", "类型二十七", "类型二十八", "类型二十九", "类型三十",
    "类型三十一", "类型三十二", "类型三十三", "类型三十四", "类型三十五", "类型三十六", "类型三十七", "类型三十八", "类型三十九", "类型四十",
    "类型四十一", "类型四十二", "类型四十三", "类型四十四", "类型四十五", "类型四十六", "类型四十七", "类型四十八", "类型四十九", "类型五十",
    "类型1", "类型2", "类型3", "类型4", "类型5", "类型6", "类型7", "类型8", "类型9", "类型10",
    "类型11", "类型12", "类型13", "类型14", "类型15", "类型16", "类型17", "类型18", "类型19", "类型20",
    "类型21", "类型22", "类型23", "类型24", "类型25", "类型26", "类型27", "类型28", "类型29", "类型30",
    "类型31", "类型32", "类型33", "类型34", "类型35", "类型36", "类型37", "类型38", "类型39", "类型40",
    "类型41", "类型42", "类型43", "类型44", "类型45", "类型46", "类型47", "类型48", "类型49", "类型50",
    // "【类型】",
    "【类型1】", "【类型2】", "【类型3】", "【类型4】", "【类型5】", "【类型6】", "【类型7】", "【类型8】", "【类型9】", "【类型10】",
    "【类型11】", "【类型12】", "【类型13】", "【类型14】", "【类型15】", "【类型16】", "【类型17】", "【类型18】", "【类型19】", "【类型20】",
    "【类型21】", "【类型22】", "【类型23】", "【类型24】", "【类型25】", "【类型26】", "【类型27】", "【类型28】", "【类型29】", "【类型30】",
    "【类型31】", "【类型32】", "【类型33】", "【类型34】", "【类型35】", "【类型36】", "【类型37】", "【类型38】", "【类型39】", "【类型40】",
    "【类型41】", "【类型42】", "【类型43】", "【类型44】", "【类型45】", "【类型46】", "【类型47】", "【类型48】", "【类型49】", "【类型50】",
    "【类型一】", "【类型二】", "【类型三】", "【类型四】", "【类型五】", "【类型六】", "【类型七】", "【类型八】", "【类型九】", "【类型十】",
    "【类型十一】", "【类型十二】", "【类型十三】", "【类型十四】", "【类型十五】", "【类型十六】", "【类型十七】", "【类型十八】", "【类型十九】", "【类型二十】",
    "【类型二十一】", "【类型二十二】", "【类型二十三】", "【类型二十四】", "【类型二十五】", "【类型二十六】", "【类型二十七】", "【类型二十八】", "【类型二十九】", "【类型三十】",
    "【类型三十一】", "【类型三十二】", "【类型三十三】", "【类型三十四】", "【类型三十五】", "【类型三十六】", "【类型三十七】", "【类型三十八】", "【类型三十九】", "【类型四十】",
    "【类型四十一】", "【类型四十二】", "【类型四十三】", "【类型四十四】", "【类型四十五】", "【类型四十六】", "【类型四十七】", "【类型四十八】", "【类型四十九】", "【类型五十】",
    "考点一", "考点二", "考点三", "考点四", "考点五", "考点六", "考点七", "考点八", "考点九", "考点十",
    "考点十一", "考点十二", "考点十三", "考点十四", "考点十五", "考点十六", "考点十七", "考点十八", "考点十九", "考点二十",
    "考点二十一", "考点二十二", "考点二十三", "考点二十四", "考点二十五", "考点二十六", "考点二十七", "考点二十八", "考点二十九", "考点三十",
    "考点三十一", "考点三十二", "考点三十三", "考点三十四", "考点三十五", "考点三十六", "考点三十七", "考点三十八", "考点三十九", "考点四十",
    "考点四十一", "考点四十二", "考点四十三", "考点四十四", "考点四十五", "考点四十六", "考点四十七", "考点四十八", "考点四十九", "考点五十",
    // "考点", "考点、", "中考真题专练",
    "考点1", "考点2", "考点3", "考点4", "考点5", "考点6", "考点7", "考点8", "考点9", "考点10",
    "考点11", "考点12", "考点13", "考点14", "考点15", "考点16", "考点17", "考点18", "考点19", "考点20",
    "考点21", "考点22", "考点23", "考点24", "考点25", "考点26", "考点27", "考点28", "考点29", "考点30",
    "考点31", "考点32", "考点33", "考点34", "考点35", "考点36", "考点37", "考点38", "考点39", "考点40",
    "考点41", "考点42", "考点43", "考点44", "考点45", "考点46", "考点47", "考点48", "考点49", "考点50",
    "【考点一】", "【考点二】", "【考点三】", "【考点四】", "【考点五】", "【考点六】", "【考点七】", "【考点八】", "【考点九】", "【考点十】",
    "【考点十一】", "【考点十二】", "【考点十三】", "【考点十四】", "【考点十五】", "【考点十六】", "【考点十七】", "【考点十八】", "【考点十九】", "【考点二十】",
    "【考点二十一】", "【考点二十二】", "【考点二十三】", "【考点二十四】", "【考点二十五】", "【考点二十六】", "【考点二十七】", "【考点二十八】", "【考点二十九】", "【考点三十】",
    "【考点三十一】", "【考点三十二】", "【考点三十三】", "【考点三十四】", "【考点三十五】", "【考点三十六】", "【考点三十七】", "【考点三十八】", "【考点三十九】", "【考点四十】",
    "【考点四十一】", "【考点四十二】", "【考点四十三】", "【考点四十四】", "【考点四十五】", "【考点四十六】", "【考点四十七】", "【考点四十八】", "【考点四十九】", "【考点五十】",
    "【考点1】", "【考点2】", "【考点3】", "【考点4】", "【考点5】", "【考点6】", "【考点7】", "【考点8】", "【考点9】", "【考点10】",
    "【考点11】", "【考点12】", "【考点13】", "【考点14】", "【考点15】", "【考点16】", "【考点17】", "【考点18】", "【考点19】", "【考点20】",
    "【考点21】", "【考点22】", "【考点23】", "【考点24】", "【考点25】", "【考点26】", "【考点27】", "【考点28】", "【考点29】", "【考点30】",
    "【考点31】", "【考点32】", "【考点33】", "【考点34】", "【考点35】", "【考点36】", "【考点37】", "【考点38】", "【考点39】", "【考点40】",
    "【考点41】", "【考点42】", "【考点43】", "【考点44】", "【考点45】", "【考点46】", "【考点47】", "【考点48】", "【考点49】", "【考点50】",
    // "【知识点】",
    "【知识点一】", "【知识点二】", "【知识点三】", "【知识点四】", "【知识点五】", "【知识点六】", "【知识点七】", "【知识点八】", "【知识点九】", "【知识点十】",
    "【知识点十一】", "【知识点十二】", "【知识点十三】", "【知识点十四】", "【知识点十五】", "【知识点十六】", "【知识点十七】", "【知识点十八】", "【知识点十九】", "【知识点二十】",
    "【知识点二十一】", "【知识点二十二】", "【知识点二十三】", "【知识点二十四】", "【知识点二十五】", "【知识点二十六】", "【知识点二十七】", "【知识点二十八】", "【知识点二十九】", "【知识点三十】",
    "【知识点三十一】", "【知识点三十二】", "【知识点三十三】", "【知识点三十四】", "【知识点三十五】", "【知识点三十六】", "【知识点三十七】", "【知识点三十八】", "【知识点三十九】", "【知识点四十】",
    "【知识点四十一】", "【知识点四十二】", "【知识点四十三】", "【知识点四十四】", "【知识点四十五】", "【知识点四十六】", "【知识点四十七】", "【知识点四十八】", "【知识点四十九】", "【知识点五十】",
    "【知识点1】", "【知识点2】", "【知识点3】", "【知识点4】", "【知识点5】", "【知识点6】", "【知识点7】", "【知识点8】", "【知识点9】", "【知识点10】",
    "【知识点11】", "【知识点12】", "【知识点13】", "【知识点14】", "【知识点15】", "【知识点16】", "【知识点17】", "【知识点18】", "【知识点19】", "【知识点20】",
    "【知识点21】", "【知识点22】", "【知识点23】", "【知识点24】", "【知识点25】", "【知识点26】", "【知识点27】", "【知识点28】", "【知识点29】", "【知识点30】",
    "【知识点31】", "【知识点32】", "【知识点33】", "【知识点34】", "【知识点35】", "【知识点36】", "【知识点37】", "【知识点38】", "【知识点39】", "【知识点40】",
    "【知识点41】", "【知识点42】", "【知识点43】", "【知识点44】", "【知识点45】", "【知识点46】", "【知识点47】", "【知识点48】", "【知识点49】", "【知识点50】",
    "【题型1】", "【题型2】", "【题型3】", "【题型4】", "【题型5】", "【题型6】", "【题型7】", "【题型8】", "【题型9】", "【题型10】",
    "【题型11】", "【题型12】", "【题型13】", "【题型14】", "【题型15】", "【题型16】", "【题型17】", "【题型18】", "【题型19】", "【题型20】",
    "【题型21】", "【题型22】", "【题型23】", "【题型24】", "【题型25】", "【题型26】", "【题型27】", "【题型28】", "【题型29】", "【题型30】",
    "【题型31】", "【题型32】", "【题型33】", "【题型34】", "【题型35】", "【题型36】", "【题型37】", "【题型38】", "【题型39】", "【题型40】",
    "【题型41】", "【题型42】", "【题型43】", "【题型44】", "【题型45】", "【题型46】", "【题型47】", "【题型48】", "【题型49】", "【题型50】",
    "【题型一】", "【题型二】", "【题型三】", "【题型四】", "【题型五】", "【题型六】", "【题型七】", "【题型八】", "【题型九】", "【题型十】",
    "【题型十一】", "【题型十二】", "【题型十三】", "【题型十四】", "【题型十五】", "【题型十六】", "【题型十七】", "【题型十八】", "【题型十九】", "【题型二十】",
    "【题型二十一】", "【题型二十二】", "【题型二十三】", "【题型二十四】", "【题型二十五】", "【题型二十六】", "【题型二十七】", "【题型二十八】", "【题型二十九】", "【题型三十】",
    "【题型三十一】", "【题型三十二】", "【题型三十三】", "【题型三十四】", "【题型三十五】", "【题型三十六】", "【题型三十七】", "【题型三十八】", "【题型三十九】", "【题型四十】",
    "【题型四十一】", "【题型四十二】", "【题型四十三】", "【题型四十四】", "【题型四十五】", "【题型四十六】", "【题型四十七】", "【题型四十八】", "【题型四十九】", "【题型五十】",
    // "1、", "2、", "3、", "4、", "5、", "6、", "7、", "8、", "9、", "10、",
    // "11、", "12、", "13、", "14、", "15、", "16、", "17、", "18、", "19、", "20、",
    // "21、", "22、", "23、", "24、", "25、", "26、", "27、", "28、", "29、", "30、",
    // "31、", "32、", "33、", "34、", "35、", "36、", "37、", "38、", "39、", "40、",
    // "41、", "42、", "43、", "44、", "45、", "46、", "47、", "48、", "49、", "50、",
    // "知识点", "必做题", "选做题", "判断", "举一反三",
    "题型1", "题型2", "题型3", "题型4", "题型5", "题型6", "题型7", "题型8", "题型9", "题型10",
    "题型11", "题型12", "题型13", "题型14", "题型15", "题型16", "题型17", "题型18", "题型19", "题型20",
    "题型21", "题型22", "题型23", "题型24", "题型25", "题型26", "题型27", "题型28", "题型29", "题型30",
    "题型31", "题型32", "题型33", "题型34", "题型35", "题型36", "题型37", "题型38", "题型39", "题型40",
    "题型41", "题型42", "题型43", "题型44", "题型45", "题型46", "题型47", "题型48", "题型49", "题型50",
};

// ============================= 配置需要设置前置空行数的指定内容 =============================
// 配置目标字段列表，指定内容所在段落仅含指定内容文本，否则不会匹配
var targetFields2 = new List<string>
{
    
    "【要点梳理】", "要点一", "要点二", "要点三", "要点四", "要点五", "要点六", "要点七", "要点八", "要点九", "要点十",
    "要点十一", "要点十二", "要点十三", "要点十四", "要点十五", "要点十六", "要点十七", "要点十八", "要点十九", "要点二十",
    "要点二十一", "要点二十二", "要点二十三", "要点二十四", "要点二十五", "要点二十六", "要点二十七", "要点二十八", "要点二十九", "要点三十",
    "【典型例题】", "类型一", "类型二", "类型三", "类型四", "类型五", "类型六", "类型七", "类型八", "类型九", "类型十",
    "类型十一", "类型十二", "类型十三", "类型十四", "类型十五", "类型十六", "类型十七", "类型十八", "类型十九", "类型二十",
    "类型二十一", "类型二十二", "类型二十三", "类型二十四", "类型二十五", "类型二十六", "类型二十七", "类型二十八", "类型二十九", "类型三十",
    "类型三十一", "类型三十二", "类型三十三", "类型三十四", "类型三十五", "类型三十六", "类型三十七", "类型三十八", "类型三十九", "类型四十",
    "类型四十一", "类型四十二", "类型四十三", "类型四十四", "类型四十五", "类型四十六", "类型四十七", "类型四十八", "类型四十九", "类型五十",
    "【类型】", "【类型1】", "【类型2】", "【类型3】", "【类型4】", "【类型5】", "【类型6】", "【类型7】", "【类型8】", "【类型9】", "【类型10】",
    "【类型11】", "【类型12】", "【类型13】", "【类型14】", "【类型15】", "【类型16】", "【类型17】", "【类型18】", "【类型19】", "【类型20】",
    "【类型21】", "【类型22】", "【类型23】", "【类型24】", "【类型25】", "【类型26】", "【类型27】", "【类型28】", "【类型29】", "【类型30】",
    "【类型31】", "【类型32】", "【类型33】", "【类型34】", "【类型35】", "【类型36】", "【类型37】", "【类型38】", "【类型39】", "【类型40】",
    "【类型41】", "【类型42】", "【类型43】", "【类型44】", "【类型45】", "【类型46】", "【类型47】", "【类型48】", "【类型49】", "【类型50】",
    "【类型一】", "【类型二】", "【类型三】", "【类型四】", "【类型五】", "【类型六】", "【类型七】", "【类型八】", "【类型九】", "【类型十】",
    "【类型十一】", "【类型十二】", "【类型十三】", "【类型十四】", "【类型十五】", "【类型十六】", "【类型十七】", "【类型十八】", "【类型十九】", "【类型二十】",
    "【类型二十一】", "【类型二十二】", "【类型二十三】", "【类型二十四】", "【类型二十五】", "【类型二十六】", "【类型二十七】", "【类型二十八】", "【类型二十九】", "【类型三十】",
    "【类型三十一】", "【类型三十二】", "【类型三十三】", "【类型三十四】", "【类型三十五】", "【类型三十六】", "【类型三十七】", "【类型三十八】", "【类型三十九】", "【类型四十】",
    "【类型四十一】", "【类型四十二】", "【类型四十三】", "【类型四十四】", "【类型四十五】", "【类型四十六】", "【类型四十七】", "【类型四十八】", "【类型四十九】", "【类型五十】",
    "【题型】", "【题型1】", "【题型2】", "【题型3】", "【题型4】", "【题型5】", "【题型6】", "【题型7】", "【题型8】", "【题型9】", "【题型10】",
    "【题型11】", "【题型12】", "【题型13】", "【题型14】", "【题型15】", "【题型16】", "【题型17】", "【题型18】", "【题型19】", "【题型20】",
    "【题型21】", "【题型22】", "【题型23】", "【题型24】", "【题型25】", "【题型26】", "【题型27】", "【题型28】", "【题型29】", "【题型30】",
    "【题型31】", "【题型32】", "【题型33】", "【题型34】", "【题型35】", "【题型36】", "【题型37】", "【题型38】", "【题型39】", "【题型40】",
    "【题型41】", "【题型42】", "【题型43】", "【题型44】", "【题型45】", "【题型46】", "【题型47】", "【题型48】", "【题型49】", "【题型50】",
    "【题型一】", "【题型二】", "【题型三】", "【题型四】", "【题型五】", "【题型六】", "【题型七】", "【题型八】", "【题型九】", "【题型十】",
    "【题型十一】", "【题型十二】", "【题型十三】", "【题型十四】", "【题型十五】", "【题型十六】", "【题型十七】", "【题型十八】", "【题型十九】", "【题型二十】",
    "【题型二十一】", "【题型二十二】", "【题型二十三】", "【题型二十四】", "【题型二十五】", "【题型二十六】", "【题型二十七】", "【题型二十八】", "【题型二十九】", "【题型三十】",
    "【题型三十一】", "【题型三十二】", "【题型三十三】", "【题型三十四】", "【题型三十五】", "【题型三十六】", "【题型三十七】", "【题型三十八】", "【题型三十九】", "【题型四十】",
    "【题型四十一】", "【题型四十二】", "【题型四十三】", "【题型四十四】", "【题型四十五】", "【题型四十六】", "【题型四十七】", "【题型四十八】", "【题型四十九】", "【题型五十】",
    "【模型】", "【模型1】", "【模型2】", "【模型3】", "【模型4】", "【模型5】", "【模型6】", "【模型7】", "【模型8】", "【模型9】", "【模型10】",
    "【模型11】", "【模型12】", "【模型13】", "【模型14】", "【模型15】", "【模型16】", "【模型17】", "【模型18】", "【模型19】", "【模型20】",
    "【模型21】", "【模型22】", "【模型23】", "【模型24】", "【模型25】", "【模型26】", "【模型27】", "【模型28】", "【模型29】", "【模型30】",
    "【模型31】", "【模型32】", "【模型33】", "【模型34】", "【模型35】", "【模型36】", "【模型37】", "【模型38】", "【模型39】", "【模型40】",
    "【模型41】", "【模型42】", "【模型43】", "【模型44】", "【模型45】", "【模型46】", "【模型47】", "【模型48】", "【模型49】", "【模型50】",
    "【模型一】", "【模型二】", "【模型三】", "【模型四】", "【模型五】", "【模型六】", "【模型七】", "【模型八】", "【模型九】", "【模型十】",
    "【模型十一】", "【模型十二】", "【模型十三】", "【模型十四】", "【模型十五】", "【模型十六】", "【模型十七】", "【模型十八】", "【模型十九】", "【模型二十】",
    "【模型二十一】", "【模型二十二】", "【模型二十三】", "【模型二十四】", "【模型二十五】", "【模型二十六】", "【模型二十七】", "【模型二十八】", "【模型二十九】", "【模型三十】",
    "【模型三十一】", "【模型三十二】", "【模型三十三】", "【模型三十四】", "【模型三十五】", "【模型三十六】", "【模型三十七】", "【模型三十八】", "【模型三十九】", "【模型四十】",
    "【模型四十一】", "【模型四十二】", "【模型四十三】", "【模型四十四】", "【模型四十五】", "【模型四十六】", "【模型四十七】", "【模型四十八】", "【模型四十九】", "【模型五十】",
    "【学习目标】", "第一部分", "第二部分", "第三部分", "第四部分", "第五部分", "第六部分", "第七部分", "第八部分", "第九部分", "第十部分",
    "第十一部分", "第十二部分", "第十三部分", "第十四部分", "第十五部分", "第十六部分", "第十七部分", "第十八部分", "第十九部分", "第二十部分",
    "必做题", "选做题", "知识点", "考向", "练习小题",
    "例题", "例题1", "例题2", "例题3", "例题4", "例题5", "例题6", "例题7", "例题8", "例题9", "例题10",
    "例题11", "例题12", "例题13", "例题14", "例题15", "例题16", "例题17", "例题18", "例题19", "例题20",
    "例题21", "例题22", "例题23", "例题24", "例题25", "例题26", "例题27", "例题28", "例题29", "例题30",
    "例题31", "例题32", "例题33", "例题34", "例题35", "例题36", "例题37", "例题38", "例题39", "例题40",
    "例题41", "例题42", "例题43", "例题44", "例题45", "例题46", "例题47", "例题48", "例题49", "例题50",
    "知识点一", "知识点二", "知识点三", "知识点四", "知识点五", "知识点六", "知识点七", "知识点八", "知识点九", "知识点十",
    "知识点十一", "知识点十二", "知识点十三", "知识点十四", "知识点十五", "知识点十六", "知识点十七", "知识点十八", "知识点十九", "知识点二十",
    "知识点二十一", "知识点二十二", "知识点二十三", "知识点二十四", "知识点二十五", "知识点二十六", "知识点二十七", "知识点二十八", "知识点二十九", "知识点三十",
    "知识点三十一", "知识点三十二", "知识点三十三", "知识点三十四", "知识点三十五", "知识点三十六", "知识点三十七", "知识点三十八", "知识点三十九", "知识点四十",
    "知识点四十一", "知识点四十二", "知识点四十三", "知识点四十四", "知识点四十五", "知识点四十六", "知识点四十七", "知识点四十八", "知识点四十九", "知识点五十",
};

var targetFields3 = new List<string>
{
    "【END】", "【END1】", "【END2】", "【END3】", "【END4】",
};

var questionTypeFields = new List<string>
{
    "一、单选题", "二、单选题", "三、单选题", "四、单选题", "五、单选题", "六、单选题", "七、单选题", "八、单选题", "九、单选题", "十、单选题",
    "十一、单选题", "十二、单选题", "十三、单选题", "十四、单选题", "十五、单选题", "十六、单选题", "十七、单选题", "十八、单选题", "十九、单选题", "二十、单选题",
    "一、多选题", "二、多选题", "三、多选题", "四、多选题", "五、多选题", "六、多选题", "七、多选题", "八、多选题", "九、多选题", "十、多选题",
    "十一、多选题", "十二、多选题", "十三、多选题", "十四、多选题", "十五、多选题", "十六、多选题", "十七、多选题", "十八、多选题", "十九、多选题", "二十、多选题",
    "一、填空题", "二、填空题", "三、填空题", "四、填空题", "五、填空题", "六、填空题", "七、填空题", "八、填空题", "九、填空题", "十、填空题",
    "十一、填空题", "十二、填空题", "十三、填空题", "十四、填空题", "十五、填空题", "十六、填空题", "十七、填空题", "十八、填空题", "十九、填空题", "二十、填空题",
    "一、解答题", "二、解答题", "三、解答题", "四、解答题", "五、解答题", "六、解答题", "七、解答题", "八、解答题", "九、解答题", "十、解答题",
    "十一、解答题", "十二、解答题", "十三、解答题", "十四、解答题", "十五、解答题", "十六、解答题", "十七、解答题", "十八、解答题", "十九、解答题", "二十、解答题",
    "一、计算题", "二、计算题", "三、计算题", "四、计算题", "五、计算题", "六、计算题", "七、计算题", "八、计算题", "九、计算题", "十、计算题",
    "十一、计算题", "十二、计算题", "十三、计算题", "十四、计算题", "十五、计算题", "十六、计算题", "十七、计算题", "十八、计算题", "十九、计算题", "二十、计算题",
    "一、实验题", "二、实验题", "三、实验题", "四、实验题", "五、实验题", "六、实验题", "七、实验题", "八、实验题", "九、实验题", "十、实验题",
    "十一、实验题", "十二、实验题", "十三、实验题", "十四、实验题", "十五、实验题", "十六、实验题", "十七、实验题", "十八、实验题", "十九、实验题", "二十、实验题",
    "一、作图题", "二、作图题", "三、作图题", "四、作图题", "五、作图题", "六、作图题", "七、作图题", "八、作图题", "九、作图题", "十、作图题",
    "十一、作图题", "十二、作图题", "十三、作图题", "十四、作图题", "十五、作图题", "十六、作图题", "十七、作图题", "十八、作图题", "十九、作图题", "二十、作图题",
    "一、综合题", "二、综合题", "三、综合题", "四、综合题", "五、综合题", "六、综合题", "七、综合题", "八、综合题", "九、综合题", "十、综合题",
    "十一、综合题", "十二、综合题", "十三、综合题", "十四、综合题", "十五、综合题", "十六、综合题", "十七、综合题", "十八、综合题", "十九、综合题", "二十、综合题",
    "一、判断题", "二、判断题", "三、判断题", "四、判断题", "五、判断题", "六、判断题", "七、判断题", "八、判断题", "九、判断题", "十、判断题",
    "十一、判断题", "十二、判断题", "十三、判断题", "十四、判断题", "十五、判断题", "十六、判断题", "十七、判断题", "十八、判断题", "十九、判断题", "二十、判断题",
    "一、选择题", "二、选择题", "三、选择题", "四、选择题", "五、选择题", "六、选择题", "七、选择题", "八、选择题", "九、选择题", "十、选择题",
    "十一、选择题", "十二、选择题", "十三、选择题", "十四、选择题", "十五、选择题", "十六、选择题", "十七、选择题", "十八、选择题", "十九、选择题", "二十、选择题",
    "一、科普阅读题", "二、科普阅读题", "三、科普阅读题", "四、科普阅读题", "五、科普阅读题", "六、科普阅读题", "七、科普阅读题", "八、科普阅读题", "九、科普阅读题",
    "十一、科普阅读题", "十二、科普阅读题", "十三、科普阅读题", "十四、科普阅读题", "十五、科普阅读题", "十六、科普阅读题", "十七、科普阅读题", "十八、科普阅读题", "十九、科普阅读题", "二十、科普阅读题",
    "一、填空与简答题", "二、填空与简答题", "三、填空与简答题", "四、填空与简答题", "五、填空与简答题", "六、填空与简答题", "七、填空与简答题", "八、填空与简答题", "九、填空与简答题",
    "十、填空与简答题", "十一、填空与简答题", "十二、填空与简答题", "十三、填空与简答题", "十四、填空与简答题", "十五、填空与简答题", "十六、填空与简答题", "十七、填空与简答题", "十八、填空与简答题", "十九、填空与简答题", "二十、填空与简答题",
    "一、综合应用题", "二、综合应用题", "三、综合应用题", "四、综合应用题", "五、综合应用题", "六、综合应用题", "七、综合应用题", "八、综合应用题", "九、综合应用题",
    "十、综合应用题", "十一、综合应用题", "十二、综合应用题", "十三、综合应用题", "十四、综合应用题", "十五、综合应用题", "十六、综合应用题", "十七、综合应用题", "十八、综合应用题", "十九、综合应用题", "二十、综合应用题",
    "一、科学探究题", "二、科学探究题", "三、科学探究题", "四、科学探究题", "五、科学探究题", "六、科学探究题", "七、科学探究题", "八、科学探究题", "九、科学探究题",
    "十、科学探究题", "十一、科学探究题", "十二、科学探究题", "十三、科学探究题", "十四、科学探究题", "十五、科学探究题", "十六、科学探究题", "十七、科学探究题", "十八、科学探究题", "十九、科学探究题", "二十、科学探究题",
    "一、填空与简答", "二、填空与简答", "三、填空与简答", "四、填空与简答", "五、填空与简答", "六、填空与简答", "七、填空与简答", "八、填空与简答", "九、填空与简答",
    "十、填空与简答", "十一、填空与简答", "十二、填空与简答", "十三、填空与简答", "十四、填空与简答", "十五、填空与简答", "十六、填空与简答", "十七、填空与简答", "十八、填空与简答", "十九、填空与简答", "二十、填空与简答",
    "一、应用题", "二、应用题", "三、应用题", "四、应用题", "五、应用题", "六、应用题", "七、应用题", "八、应用题", "九、应用题", "十、应用题",
    "十一、应用题", "十二、应用题", "十三、应用题", "十四、应用题", "十五、应用题", "十六、应用题", "十七、应用题", "十八、应用题", "十九、应用题", "二十、应用题",
    "一、简答题", "二、简答题", "三、简答题", "四、简答题", "五、简答题", "六、简答题", "七、简答题", "八、简答题", "九、简答题", "十、简答题",
    "十一、简答题", "十二、简答题", "十三、简答题", "十四、简答题", "十五、简答题", "十六、简答题", "十七、简答题", "十八、简答题", "十九、简答题", "二十、简答题",
    "一、作图题", "二、作图题", "三、作图题", "四、作图题", "五、作图题", "六、作图题", "七、作图题", "八、作图题", "九、作图题", "十、作图题",
    "十一、作图题", "十二、作图题", "十三、作图题", "十四、作图题", "十五、作图题", "十六、作图题", "十七、作图题", "十八、作图题", "十九、作图题", "二十、作图题",
};

// ====================== 配置需要标准化的序号字段 ======================
// 包含需要将英文括号替换为中文括号的序号字段列表
// 例如：将"(1)"替换为"（1）"，将"(a)"替换为"（a）"
var sequenceFields = new List<string>
{
    "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20",
    "a", "b", "c", "d", "e", "f", "g",
    "A", "B", "C", "D", "E", "F", "G",
    "i", "ii", "iii", "iv", "v", "vi", "vii", "viii",
    "I", "II", "III", "IV", "V", "VI", "VII", "VIII",
    "Ⅰ", "Ⅱ", "Ⅲ", "Ⅳ", "Ⅴ", "Ⅵ", "Ⅶ", "Ⅷ", "Ⅸ", "Ⅹ",
};

// ====================================== 配置页边距 ======================================
// 括号里直接填以厘米为单位的数值，会自动转换为磅，因为 Aspose.Words 不支持厘米单位
double topMargin      = UnitConverter.CmToPoints(1.59);     // 设置上边距
double bottomMargin   = UnitConverter.CmToPoints(1.59);     // 设置下边距
double leftMargin     = UnitConverter.CmToPoints(3.52);     // 设置左边距
double rightMargin    = UnitConverter.CmToPoints(3.52);     // 设置右边距
double headerDistance = UnitConverter.CmToPoints(0.88);     // 设置页眉顶端距离
double footerDistance = UnitConverter.CmToPoints(0.88);     // 设置页脚底端距离

// ● ○ ├─ ◆ ✖ ⚠ ✔ ➤

// ==================================== 目标路径存在性检查 ====================================
// 获取规范化后的绝对路径（处理相对路径和多余分隔符），如果是相对路径，Path.GetFullPath 可将其转换为绝对路径
var normalizedPath = Path.GetFullPath(sourceFolderPath);

// ========================== 检测输入类型并验证存在性 ==========================
// 智能检测输入是文件夹还是文件，并进行相应的存在性验证
bool isInputFile = false;
if (File.Exists(normalizedPath))
{
    // ==================== 输入为文件的情况 ====================
    isInputFile = true;
    // 验证文件扩展名是否为.docx
    if (!normalizedPath.EndsWith(".docx", StringComparison.OrdinalIgnoreCase))
    {
        Console.ForegroundColor = ConsoleColor.Red;
        Console.WriteLine($"错误：目标文件不是Word文档！（路径：{normalizedPath}）");
        Console.ResetColor();
        Console.WriteLine("程序已终止");
        return;
    }
}
else if (Directory.Exists(normalizedPath))
{
    // ==================== 输入为文件夹的情况 ====================
    isInputFile = false;
}
else
{
    // ==================== 输入路径不存在 ====================
    Console.ForegroundColor = ConsoleColor.Red; // 红色高亮错误
    Console.WriteLine($"错误：目标路径不存在！（路径：{normalizedPath}）");
    Console.ResetColor();
    Console.WriteLine("程序已终止");
    return; // 直接终止程序
}

// =============================================== 检查目标文件夹层级 ===============================================
// 功能：如果目标文件夹层级小于指定数，则需要确认操作，防止目标文件夹设置错误而对浅层文件夹进行处理
// 注意：仅对文件夹输入进行层级检查，单文件输入跳过此检查
if (!isInputFile)
{
    // 拆分路径层级（兼容Windows和Unix路径分隔符）
    var pathLevels = normalizedPath.Split(Path.DirectorySeparatorChar, StringSplitOptions.RemoveEmptyEntries);

    // 计算实际文件夹层级数（排除根目录的特殊情况）
    var depth = pathLevels.Length;

    // 如果层级数不足5层
    if (depth < 5)
    {
        Console.ForegroundColor = ConsoleColor.Yellow; // 高亮提示
        Console.WriteLine($"警告：目标文件夹层级数仅为 {depth} 层，请谨慎操作！（路径：{normalizedPath}）");
        Console.ResetColor();

        // 循环直到输入有效选项
        while (true)
        {
            Console.Write("是否继续执行？（Y/N）: ");
            var input = Console.ReadLine()?.Trim().ToLower();
            if (input == "n")
            {
                Console.WriteLine("程序已终止");
                return;     // 直接退出程序
            }
            if (input == "y") break;    // 继续执行
            Console.WriteLine("无效输入，请键入 Y 或 N");
        }
    }
}

// ====================== 输出目标路径信息 ======================
Console.ForegroundColor = ConsoleColor.DarkBlue;
if (isInputFile)
{
    Console.WriteLine($"○ 处理中...源文件路径：【{normalizedPath}】");
}
else
{
    Console.WriteLine($"○ 处理中...源文件夹路径：【{normalizedPath}】");
}
Console.ResetColor();

// =========================== 创建备份 ===========================
// 根据输入类型选择相应的备份策略：文件夹备份或单文件备份
// 执行备份操作，通过返回值判断备份成功与否，备份成功继续往下执行，备份失败直接退出程序
var backupService = new BackupService(); 

// 生成时间戳备份路径
// 使用精确到秒的时间戳保证路径唯一性
var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
string backupPath;

Console.ForegroundColor = ConsoleColor.DarkMagenta;
if (isInputFile)
{
    // ==================== 单文件备份逻辑 ====================
    // 备份文件放在源文件同一目录下，文件名格式：原文件名_备份_时间戳.docx
    var sourceDir = Path.GetDirectoryName(normalizedPath)!;
    var sourceFileName = Path.GetFileNameWithoutExtension(normalizedPath);
    var sourceExtension = Path.GetExtension(normalizedPath);
    backupPath = Path.Combine(sourceDir, $"{sourceFileName}_备份_{timestamp}{sourceExtension}");
    
    if (backupService.CreateSingleFileBackup(normalizedPath, backupPath))
    {
        Console.WriteLine($"○ 备份成功！备份文件路径：【{backupPath}】");
    }
    else
    {
        Console.WriteLine("○ 备份失败，终止处理！");
        return;
    }
}
else
{
    // ==================== 文件夹备份逻辑 ====================
    backupPath = $"{sourceFolderPath}_备份_{timestamp}";
    
    // ================ 显式创建备份根目录 ================
    // 作用：确保即使源目录为空或仅含空目录时，备份目录仍能被创建
    // 设计要点：先于任何文件操作创建根目录，保证备份标记存在
    Directory.CreateDirectory(backupPath);
    
    if (backupService.CreateBackup(sourceFolderPath, backupPath))
    {
        Console.WriteLine($"○ 备份成功！备份文件夹路径：【{backupPath}】");
    }
    else
    {
        Console.WriteLine("○ 备份失败，终止处理！");
        return;
    }
}
Console.ResetColor();

// ====================================== 加载 Aspose.Words 许可证 =======================================
try
{
    // 创建许可证实例（Aspose的标准操作方式）
    var license = new License();  // Aspose.Words.License 类实例
    
    // 设置许可证文件路径，可将许可证文件放在项目根目录，使用相对路径更安全
    license.SetLicense("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Library/Aspose.Words/Aspose.Words_v25.4.0_net9.0/Aspose.Total.NET.lic"); 
    
    // 输出 Aspose.Words 版本
    Console.ForegroundColor = ConsoleColor.Magenta;
    var asposeAssembly = typeof(Document).Assembly;
    Console.WriteLine($"○ Aspose.Words 加载成功！版本: {asposeAssembly.GetName().Version}");
    Console.ResetColor();
}
catch (Exception ex)
{
    // 许可证加载失败时立即终止整个程序
    Console.ForegroundColor = ConsoleColor.Red;
    Console.WriteLine($"○ Aspose.Words 许可证加载失败: {ex.Message}");
    Console.ResetColor();
    return;
}

// ======================================== 输出 CPU 核心数和执行线程数 ========================================
Console.ForegroundColor = ConsoleColor.DarkYellow;
Console.WriteLine($"○ CPU 核心数 {Environment.ProcessorCount}，程序并行处理线程数 {Environment.ProcessorCount}");
Console.ResetColor();

// ==================================== 获取待处理文件列表 ====================================
List<string> filePaths;
if (isInputFile)
{
    // ==================== 单文件处理模式 ====================
    // 直接使用输入的文件路径
    filePaths = new List<string> { normalizedPath };
}
else
{
    // ==================== 文件夹处理模式 ====================
    // 递归获取文件夹下所有.docx文件
    filePaths = Directory.EnumerateFiles(sourceFolderPath, "*.docx",
        SearchOption.AllDirectories).ToList();
}

// ==================== 输出待处理文件信息 ====================
Console.ForegroundColor = ConsoleColor.DarkCyan;
if (isInputFile)
{
    Console.WriteLine($"○ 待处理文件: 【{Path.GetFileName(normalizedPath)}】");
}
else
{
    Console.WriteLine($"○ 源文件夹 Word 文件总数: {filePaths.Count}");
}
Console.ResetColor();

// ======================== 添加进度追踪变量 ========================
int processedCount = 0;
int totalCount = filePaths.Count;
object progressLock = new object(); // 线程安全的进度显示锁
object errorLock = new object(); // 线程安全的错误信息输出锁

// ====================== 开始处理提示 ======================
Console.ForegroundColor = ConsoleColor.Green;
Console.WriteLine($"○ 开始处理，共 {totalCount} 个文件");
Console.ResetColor();

// ====================== 自定义进度显示方法 ======================
void UpdateProgress(int current, int total)
{
    lock (progressLock)
    {
        // 计算百分比
        double percentage = (double)current / total * 100;
        
        // 创建进度条
        int barWidth = 50;
        int filledWidth = (int)(percentage / 100 * barWidth);
        string progressBar = "█".PadRight(filledWidth, '█').PadRight(barWidth, '░');
        
        // 保存当前光标位置并移动到进度行
        int currentLine = Console.CursorTop;
        Console.SetCursorPosition(0, currentLine);
        
        // 显示进度条和百分比
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.Write($"\r进度: [{progressBar}] {current}/{total} ({percentage:F1}%)");
        Console.ResetColor();
        
        // 如果处理完成，换行
        if (current >= total)
        {
            Console.WriteLine();
        }
    }
}

// 显示初始进度
UpdateProgress(0, totalCount);

// ========================================== 初始化处理步骤管道 ==========================================
// 多个实现了同一接口 IPipelineStep 的对象可以被存储在一个集合 pipelineSteps 中，
// 可以通过接口类型的变量 step 进行引用
// 如果是普通的对象只能以自身的类型被引用，无法赋值给其他类型的变量
var pipelineSteps = new List<IPipelineStep>  
{
    // ===================================================== 执行批次 1 =====================================================
    new ReplaceTextStep(replaceDict),                                         // 字符替换步骤
    new RemoveQuestionSourceStep(),                                           // 按需选择！删除题目来源步骤，格式为题号前由括号包含的一段文本，仅在特殊讲义中执行
    // new RemoveSpecialQuestionSourceStep(),                                    // 按需选择！删除特殊题目来源步骤，格式为“图片+来源”或“图片+题号+来源”，仅在特殊讲义中执行
    new SetParagraphIndentStep(indentCharacters, 0.53),               // 按需选择！设置选择题选项段落缩进步骤
    new InsertEndTagStep(targetFields, fontSettings["latinFont"], fontSettings["cjkFont"], false),  // 插入题型标签【END】步骤，true为跳过所有图片段落，false只跳过“空行+图片段落”
    new InsertMultipleChoiceTagStep(),                                        // 插入多选题标签"[多选题]"步骤
    new InsertDifficultyStarsStep(fontSettings["cjkFont"]),                          // 插入难度星级标签步骤
    // new RemoveQuestionTypeStep(questionTypeFields),                                 // 按需选择！删除题型提示所在段落及上下空行步骤
    new InsertBracketsStep(),                                                 // 智能识别选择题并插入空括号步骤
    new NormalizeUnderlineStep(),                                             // 标准化填空下划线步骤
    new SetPageMarginsStep(topMargin, bottomMargin, leftMargin,
                        rightMargin, headerDistance, footerDistance),         // 页边距设置步骤
    new SetPageNumberStep(),                                                  // 删除页脚并插入新页脚步骤
    // new InsertQuestionInfoStep(null, 1),                                      // 按需选择！插入试题信息步骤（"［2025·广东卷］"）
    new NormalizeBracketStep(),                                               // 标准化括号步骤
    new NormalizeSequenceStep(sequenceFields),                               // 标准化序号步骤（序号的英文括号转中文括号）
    new SetAnswerIndentStep("blank.on"),                                   // 设置答案区域缩进步骤，"blank.on"为启用段落开头空格删除；"blank.off"为关闭空格删除
    new SetAnswerShadingStep("white"),                                     // 设置答案底纹步骤，"gray"为设置灰色底纹；"white"为设置白色底纹
    new NormalizeTableStep("gray.off", 24),                                  // 标准化表格步骤，"gray.on"为启用答案区域表格灰色底纹；"gray.off"为关闭灰色底纹，24为字符数阈值（单元格字符数>=此值时居左对齐）
    new SetEmptyParagraphsStep(markersToAdjust, spacesToAdjust),              // 设置指定字段前置空行数步骤
    new SetQuestionSpacesStep(1, questionSkipFields, null),             // 按需选择！设置题号前置空间步骤（跳过指定字段往前设置），一般只需在不执行【END】标签插入步骤的文档（如讲义）中执行
    new SetEmptyParagraphsStep(questionTypeFields, 1),              // 按需选择！与下一步骤二选一！设置questionTypeFields字段（题型提示）前置空行数步骤
    new SetEmptyParagraphsStep(targetFields, 1),              // 按需选择！与上一步骤二选一！设置targetFields字段前置空行数步骤，一般在特殊讲义中执行
    // new SetEmptyParagraphsStep(targetFields2, 1),              // 按需选择！设置targetFields2字段前置空行数步骤，仅在特殊讲义中执行
    new SetEmptyParagraphsStep("【难度】", 0),              // 按需选择！设置【难度】字段前置空行数为0步骤，较为普遍，可全部执行
    // new DeleteFirstParagraphsStep(2),                                         // 从文档开头删除指定空行数步骤,注意删除行数要与是否启用删除题型提示联动设置！
    new FormatSpecificTextStep("【答案】", null, null, null, null, false,false),     // 设置【答案】为非斜体
    new SetFontsStep(fontSettings["latinFont"],
                     fontSettings["cjkFont"],
                     fontSettings["fontSize"],                                // 全局字体替换步骤
                     true),                                                  // true为跳过标题字体替换，false为正常替换所有字体
    // new InsertFileNameStep(fieldsToRemove, true, 1, 0, 1, 1, 1, 1),           // 插入文件名到文档开头作为标题步骤，1、0控制双空格换行
    new NormalizeTitleStep(1, TitleFontWeight.Bold),                     // 标准化标题步骤（空行数, 字体粗细: Bold=粗体, Normal=细体, Unchanged=不变）
    new NormalizeMathStep(mathFontSettings["mathLatinFont"],
                       mathFontSettings["mathLatinFontSize"],
                       mathFontSettings["mathCjkFont"],
                       mathFontSettings["mathCjkFontSize"]),                 // 公式标准化步骤
    new NormalizeMathFunctionStep(mathFunctions),                            // 数学函数和单位标准化步骤（设置公式中的指定字段为非斜体）
    new NormalizeMathFunctionStep(physicsFunctions),                         // 按需选择！初中物理专用！函数和单位标准化步骤（设置公式中的指定字段为非斜体）
    // new NormalizeMathFunctionStep(chemistryFunctions),                       // 按需选择！初中化学专用！函数和单位标准化步骤（设置公式中的指定字段为非斜体）
    new NormalizeCalculusStep(calculusSymbols),                              // 微积分符号标准化步骤（设置公式中微积分的d为非斜体）
    new DisableDocumentGridStep(),                                           // 设置无网格步骤
    new SetLineSpacingStep(),                                                // 设置行距步骤

    // =============================================== 执行批次 2 ===============================================
    // 【非学生版】制作，跟 批次3 二选一，不能与 批次1 同时执行
    // 1.在"执行批次 1"执行完后，依次打开文档，全选 -> 字体改为"XITS Math"
    // 2.检查修正标题
    // 3.检查修正【END】标签插入位置和文档其他内容格式
    // 4.第2步 和 第3步 如果出现共性问题，可用程序再次执行，没有问题后再执行下一步
    // 5.如果 第4步 未执行，则直接执行本批次；如果 第4步 执行了，则全选 -> 字体改为"XITS Math"，再执行本批次
    // new SetPunctuationStep(fontSettings["latinFont"], fontSettings["cjkFont"]),   // 设置标点及特殊符号格式步骤
    // new SetLineSpacingStep(),                                                     // 设置行距步骤
    
    // ============================================== 执行批次 3 ==============================================
    // 【学生版】制作，跟批次2二选一，不能与 批次1 同时执行
    // 此执行批次为制作学生版，需独立执行，且必须在文档已添加【END】标签且标签位置无误的前提下才可执行
    // 1.在"执行批次 1"执行完后，依次打开文档，全选 -> 字体改为"XITS Math"
    // 2.检查修正标题
    // 3.检查修正【END】标签插入位置和文档其他内容格式
    // 4.第2步 和 第3步 如果出现共性问题，可用程序再次执行，没有问题后再执行下一步
    // 5.如果 第4步 未执行，则直接执行本批次；如果 第4步 执行了，则全选 -> 字体改为"XITS Math"，再执行本批次
    // new SetPunctuationStep(fontSettings["latinFont"], fontSettings["cjkFont"]),     // 设置标点及特殊符号格式步骤
    // new CopyAndProcessDocumentStep(1,1,1,1),     // 制作学生版步骤
    // new SetLineSpacingStep(),                                                        // 设置行距步骤
    
    // ============================================ 执行批次 4 ============================================
    // 此批次按需执行！在【END】标签前添加题目来源标记（如"【CSZMN】"）或做题标记（如"【1】"）步骤
    // field1Patterns：需要查找的不同【END】标签；field2：需要在【END】前插入的标签；lookBackCout：往前查找的段落数
    // new InsertTagBeforeEndStep(
    //     field1Patterns: new[] { "【END1】", "【END2】", "【END3】", "【END4】" },
    //     field2: "【CSZMNN】",
    //     lookBackCount: 3),

    // ============================================ 执行批次 5 ============================================
    // 此批次按需执行！学生版更新，即学生版的第二个section修改后执行此批次，可将第一个section自动更新
    // new RemoveFirstSectionStep(),                                             // 删除第一个section步骤
    // // new RenumberQuestionsStep(),                                              // 按需执行！题号重新排列步骤
    // new InsertMultipleChoiceTagStep(),                                        // 插入多选题标签"[多选题]"步骤
    // new InsertBracketsStep(),                                                 // 智能识别选择题并插入空括号步骤
    // new NormalizeUnderlineStep(),                                             // 标准化填空下划线步骤
    // new SetPageMarginsStep(topMargin, bottomMargin, leftMargin,
    //                     rightMargin, headerDistance, footerDistance),         // 页边距设置步骤
    // new SetPageNumberStep(),                                                  // 删除页脚并插入新页脚步骤
    // new ReplaceTextStep(replaceDict),                                         // 字符替换步骤
    // new NormalizeBracketStep(),                                               // 括号标准化步骤
    // new NormalizeSequenceStep(sequenceFields),                               // 序号标准化步骤（英文括号转中文括号）
    // new SetEmptyParagraphsStep(markersToAdjust, spacesToAdjust),              // 设置指定字段前置空行数步骤（支持多标识符）
    // new FormatSpecificTextStep("【答案】", null, null, null, null, false),     // 设置【答案】为非斜体
    // new SetFontsStep(fontSettings["latinFont"],
    //                  fontSettings["cjkFont"],
    //                  fontSettings["fontSize"],                                // 全局字体替换步骤
    //                  true),                                                   // true为跳过标题字体替换，false为正常替换所有字体
    // new NormalizeTitleStep(1, TitleFontWeight.Unchanged),                     // 标准化标题步骤（空行数, 字体粗细: Bold=粗体, Normal=细体, Unchanged=不变）
    // new NormalizeMathStep(mathFontSettings["mathLatinFont"],
    //                    mathFontSettings["mathLatinFontSize"],
    //                    mathFontSettings["mathCjkFont"],
    //                    mathFontSettings["mathCjkFontSize"]),                  // 公式标准化步骤
    // new NormalizeMathFunctionStep(mathFunctions),                            // 数学函数标准化步骤（设置公式中的指定字段为非斜体）
    // new NormalizeCalculusStep(calculusSymbols),                             // 微积分符号标准化步骤（设置公式中的d为非斜体）
    // new DisableDocumentGridStep(),                                            // 设置无网格步骤
    // new SetLineSpacingStep(),                                                 // 设置行距步骤
    // new SetPunctuationStep(fontSettings["latinFont"],
    //                        fontSettings["cjkFont"]),                          // 设置标点及特殊符号格式步骤
    // new SetPunctuationStep(fontSettings["latinFont"],
    //                        fontSettings["cjkFont"]),                          // 设置标点及特殊符号格式步骤
    // new CopyAndProcessDocumentStep(0,0,0,0),                                  // 制作学生版步骤
    // new SetLineSpacingStep(),                                                 // 设置行距步骤

    // ============================================ 执行批次 6 ============================================
    // 此批次按需执行！应对需要单独执行的步骤或临时步骤组合
    // new RemoveFirstSectionStep(),                                             // 删除第一个section步骤
    // new ReplaceTextStep(replaceDict3),                                         // 字符替换步骤
    // new InsertEndTagStep(targetFields, fontSettings["latinFont"], fontSettings["cjkFont"], false),  // 插入题型标签【END】步骤
    // // // new SetPunctuationStep(fontSettings["latinFont"], fontSettings["cjkFont"]),   // 设置标点及特殊符号格式步骤
    // // new InsertDifficultyStarsStep(fontSettings["cjkFont"]),                          // 插入难度星级标签步骤
    // new SetEmptyParagraphsStep(targetFields, 1),              // 按需选择！设置targetFields字段前置空行数步骤，仅在特殊讲义中执行
    // new SetEmptyParagraphsStep(targetFields3, 0),              // 按需选择！设置targetFields字段前置空行数步骤，仅在特殊讲义中执行
    // new SetEmptyParagraphsStep("考点攻略", 0),              // 按需选择！设置指定字段前置空行数步骤，仅在特殊讲义中执行
    // new NormalizeTitleStep(1, TitleFontWeight.Bold),                     // 标准化标题步骤（空行数, 字体粗细: Bold=粗体, Normal=细体, Unchanged=不变）
    // new SetQuestionSpacesStep(1, questionSkipFields, null),             // 按需选择！设置题号前置空间步骤（跳过指定字段往前设置），一般只需在不执行【END】标签插入步骤的文档（如讲义）中执行
    // new SetPunctuationStep(fontSettings["latinFont"], fontSettings["cjkFont"]),     // 设置标点及特殊符号格式步骤
    // new CopyAndProcessDocumentStep(1,1,1,1),     // 制作学生版步骤
    // new SetLineSpacingStep(),                                                        // 设置行距步骤
};

// ================================= 配置并行处理参数 =================================
// Mac mini M2是8核CPU，4个性能核和4个能效核，但能效核速度太慢，处理 CPU 密集型程序只用性能核
// 所以最大并发数设置为4～8都可以，实测设置为8时用时最短，实测设置为3～16的时间相差不多（15%以内）
// 这里动态设置最大并发数为CPU核心数
var parallelOptions = new ParallelOptions
{
    MaxDegreeOfParallelism = Math.Max(1, Environment.ProcessorCount),
};

// ========================================= 智能并行处理文件 ===========================================
// 支持单文件和多文件两种处理模式，自动根据输入类型调整处理策略
try
{
    // ======================================== 核心智能并行处理逻辑 ========================================
    // 作用：Parallel.ForEach 会自动并行遍历 filePaths 中的每个元素，无需手动编写循环，利用多线程加速处理，
    //      单文件模式：filePaths 只包含1个文件，处理逻辑与多文件一致，但无并行优势
    //      多文件模式：会将 files 分割成多个块（chunk），并为每个块分配一个线程处理。若 filePaths 包含 100 个文件，
    //                且 MaxDegreeOfParallelism = 4，则可能同时启动 4 个线程，每个线程处理约 25 个文件。
    //      文件处理顺序不固定，出现异常时，Parallel.ForEach 不会执行新的线程，但已开始的线程会执行完。
    // filePaths：待处理的 Word 文档文件路径集合，单文件或多文件，是 List<string> 类型
    // parallelOptions：配置并行度（如最大线程数），是 ParallelOptions 类型
    // file => { ... }：每个文件处理逻辑的 Lambda 表达式，是Action委托
    Parallel.ForEach(filePaths, parallelOptions, filePath =>
    {
        try
        {
            Document? doc = null;
            
            try
            {
                // ======================== 文档加载 ========================
                // Aspose.Words 的 Document 构造函数会自动锁定文件
                doc = new Document(filePath);
            }
            catch (Exception ex)
            {
                lock (errorLock)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"✗ 文档加载失败: [{Path.GetFileName(filePath)}] - {ex.Message}");
                    Console.ResetColor();
                }
                throw;
            }
            
            // ================================= 管道处理流程 ===================================
            // 每个步骤独立且无状态，确保线程安全
            // step 是一个 IPipelineStep 类型的变量，它可以依次指向集合 pipelineSteps 中的每个对象，
            // 如 InsertFileNameStep、RemoveFooterStep 等，通过接口实现了统一调用。
            foreach (var step in pipelineSteps)
            {
                try
                {
                    // ============================== 管道处理异常判断 ==============================
                    // step 的返回值有 true 和 false，返回 false 时执行 if 语句；返回 true 时跳过 if 语句
                    if (!step.Execute(doc, filePath))
                    {
                        // ============================= 管道异常处理 =============================
                        // 某管道步骤执行失败时，在控制台输出"路径+文件名+失败步骤信息"，并抛出异常
                        lock (errorLock)
                        {
                            Console.ForegroundColor = ConsoleColor.Red;
                            Console.WriteLine($"✗ 步骤执行失败: [{Path.GetFileName(filePath)}] - {step.GetType().Name}");
                            Console.ResetColor();
                        }
                        throw new Exception($"Step {step.GetType().Name} failed for file {Path.GetFileName(filePath)}");
                    }
                }
                catch (Exception ex) when (!(ex.Message.Contains("Step") && ex.Message.Contains("failed")))
                {
                    // 捕获步骤执行过程中的异常（不是上面手动抛出的异常）
                    lock (errorLock)
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"✗ 步骤执行异常: [{Path.GetFileName(filePath)}] - {step.GetType().Name} - {ex.Message}");
                        Console.ResetColor();
                    }
                    throw;
                }
            }
            
            // ============ 文档保存 ============
            // Save() 方法会刷新缓冲区并释放部分资源
            try
            {
                doc.Save(filePath);
            }
            catch (Exception ex)
            {
                lock (errorLock)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"✗ 文档保存失败: [{Path.GetFileName(filePath)}] - {ex.Message}");
                    Console.ResetColor();
                }
                throw;
            }
            
            // ============================ 进度更新 ============================
            // 使用线程安全的计数器更新进度显示
            var currentCount = Interlocked.Increment(ref processedCount);
            
            // 更新进度条显示
            UpdateProgress(currentCount, totalCount);
        }
        catch (Exception ex)
        {
            // 捕获整个文件处理过程中的任何未处理异常
            lock (errorLock)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"✗ 文件处理失败: [{Path.GetFileName(filePath)}] - {ex.Message}");
                Console.ResetColor();
            }
            throw;
        }
        
    });
    
    // ====================== 完成提示 ======================
    Console.ForegroundColor = ConsoleColor.Green;
    Console.WriteLine("✓ 全部文件处理完毕！");
    Console.ResetColor();
}
catch (Exception ex)
{
    // ================== 异常时的控制台反馈 ==================
    Console.WriteLine(); // 换行确保进度条显示完整
    Console.ForegroundColor = ConsoleColor.Red;
    Console.WriteLine("✗ 出现异常，未处理完毕！");
    Console.ResetColor();
    
    // =========================== 异常判断和详细信息输出 ===========================
    // 如果捕获的异常不是 AggregateException，则执行 if ；如果是，则跳过 if
    if (ex is not AggregateException)
    {
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine($"程序出现异常！异常信息: {ex.Message}");
        Console.ResetColor();
    }
    else
    {
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("程序在并行处理过程中出现异常！");
        Console.WriteLine($"异常信息: {ex.Message}");
        Console.ResetColor();
    }
    
    // ========================= 回滚操作 =========================
    // 出现任何异常都要执行回滚操作
    Console.ForegroundColor = ConsoleColor.Cyan;
    Console.WriteLine("处理失败！正在执行回滚操作...");
    Console.ResetColor();
    RollbackService.Execute(normalizedPath, backupPath);
}