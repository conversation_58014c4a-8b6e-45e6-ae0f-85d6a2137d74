using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Tables;
using Aspose.Words.Math;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 标准化图片处理步骤（实现IPipelineStep接口）
/// 功能：识别图片段落并对图片进行标准化处理，包括大小调整、空格删除和对齐方式设置
/// </summary>
public class NormalizeImageStep : IPipelineStep
{
    private readonly double _minimumWidth;
    private readonly double _standardWidth;
    private readonly string _removeBlank;
    private readonly string _alignment;

    /// <summary>
    /// 构造函数：初始化图片标准化参数
    /// </summary>
    /// <param name="minimumWidth">最小宽度（厘米）</param>
    /// <param name="standardWidth">标准宽度（厘米）</param>
    /// <param name="removeBlank">空格删除模式：removeBlank.all（删除所有图片段落空格）、removeBlank.standard（仅删除标准宽度图片段落空格）、removeBlank.off（关闭删除功能）</param>
    /// <param name="alignment">对齐方式：alignment.left.all（所有图片段落左对齐）、alignment.left.standard（仅标准宽度图片段落左对齐）、alignment.middle.all（所有图片段落居中）、alignment.middle.standard（仅标准宽度图片段落居中）、alignment.right.all（所有图片段落右对齐）、alignment.right.standard（仅标准宽度图片段落右对齐）、alignment.null（不设置对齐方式）</param>
    public NormalizeImageStep(double minimumWidth, double standardWidth, string removeBlank, string alignment)
    {
        _minimumWidth = minimumWidth;
        _standardWidth = standardWidth;
        _removeBlank = removeBlank ?? "removeBlank.off";
        _alignment = alignment ?? "alignment.null";
    }

    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>是否处理成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取文档中所有段落（不在页眉页脚中的段落）
            var paragraphs = GetNonHeaderFooterParagraphs(doc);
            
            // 遍历处理每个段落
            foreach (Paragraph paragraph in paragraphs)
            {
                // 跳过表格内的段落
                if (IsInTable(paragraph)) continue;
                
                // 检查是否为图片段落
                if (IsImageParagraph(paragraph))
                {
                    ProcessImageParagraph(paragraph);
                }
            }
            
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 获取文档中所有非页眉页脚的段落
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>段落列表</returns>
    private List<Paragraph> GetNonHeaderFooterParagraphs(Document doc)
    {
        var paragraphs = new List<Paragraph>();
        
        // 获取所有段落节点
        NodeCollection allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true);
        
        foreach (Paragraph para in allParagraphs.OfType<Paragraph>())
        {
            // 跳过页眉页脚内容
            if (!IsInHeaderFooter(para))
            {
                paragraphs.Add(para);
            }
        }
        
        return paragraphs;
    }

    /// <summary>
    /// 检查段落是否在页眉页脚中
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否在页眉页脚中</returns>
    private static bool IsInHeaderFooter(Paragraph para)
    {
        return para.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// <summary>
    /// 检查段落是否在表格中
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否在表格中</returns>
    private static bool IsInTable(Paragraph para)
    {
        return para.GetAncestor(typeof(Table)) != null;
    }

    /// <summary>
    /// 检查段落是否为图片段落
    /// 图片段落定义：段落中除了一张图片，最多只有图片前后的若干个空格，没有其他字符，且不包含OMML公式
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否为图片段落</returns>
    private bool IsImageParagraph(Paragraph para)
    {
        bool hasImage = false;
        bool hasOmml = false;
        
        // 检查所有子节点
        foreach (Node node in para.GetChildNodes(NodeType.Any, true))
        {
            switch (node.NodeType)
            {
                case NodeType.Shape:
                    Shape shape = (Shape)node;
                    if (shape.ImageData.HasImage)
                    {
                        hasImage = true;
                    }
                    break;
                    
                case NodeType.GroupShape:
                    hasImage = true;
                    break;
                    
                case NodeType.OfficeMath:
                    hasOmml = true;
                    break;
                    
                case NodeType.Run:
                    Run run = (Run)node;
                    string text = run.Text ?? "";
                    
                    // 检查Run中的文本是否只包含空格（支持各种Unicode空格）
                    if (!string.IsNullOrEmpty(text) && !IsOnlyWhitespace(text))
                    {
                        // 如果包含非空格字符，则不是图片段落
                        return false;
                    }
                    break;
                    
                default:
                    // 如果包含其他类型的节点（除了段落本身），可能不是图片段落
                    // 但某些节点类型（如BookmarkStart/End）可以忽略
                    if (node.NodeType != NodeType.Paragraph && !IsIgnorableNodeType(node.NodeType))
                    {
                        return false;
                    }
                    break;
            }
        }
        
        // 如果包含OMML公式，则不是图片段落
        if (hasOmml)
        {
            return false;
        }
        
        // 只有包含图片且没有其他重要内容才是图片段落
        return hasImage;
    }

    /// <summary>
    /// 检查字符串是否只包含空白字符（支持各种Unicode空格）
    /// </summary>
    /// <param name="text">要检查的文本</param>
    /// <returns>是否只包含空白字符</returns>
    private bool IsOnlyWhitespace(string text)
    {
        if (string.IsNullOrEmpty(text))
            return true;
            
        // 使用正则表达式检查是否只包含空白字符
        // \s 匹配所有Unicode空白字符，包括：
        // - 普通空格 (U+0020)
        // - 制表符 (U+0009)
        // - 换行符 (U+000A, U+000D)
        // - 全角空格 (U+3000)
        // - 其他Unicode空格字符
        return Regex.IsMatch(text, @"^\s*$", RegexOptions.Compiled);
    }

    /// <summary>
    /// 检查节点类型是否可以忽略
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>是否可以忽略</returns>
    private bool IsIgnorableNodeType(NodeType nodeType)
    {
        return nodeType == NodeType.BookmarkStart ||
               nodeType == NodeType.BookmarkEnd ||
               nodeType == NodeType.CommentRangeStart ||
               nodeType == NodeType.CommentRangeEnd ||
               nodeType == NodeType.FieldStart ||
               nodeType == NodeType.FieldSeparator ||
               nodeType == NodeType.FieldEnd;
    }

    /// <summary>
    /// 处理图片段落
    /// </summary>
    /// <param name="paragraph">图片段落</param>
    private void ProcessImageParagraph(Paragraph paragraph)
    {
        // 获取段落中的所有图片
        var shapes = paragraph.GetChildNodes(NodeType.Shape, true).OfType<Shape>()
            .Where(s => s.ImageData.HasImage).ToList();

        if (shapes.Count == 0) return;

        // 处理每个图片（虽然按定义应该只有一个）
        foreach (Shape shape in shapes)
        {
            // 1. 调整图片大小
            bool isStandardWidth = AdjustImageSize(shape);

            // 2. 根据removeBlank参数删除空格
            ProcessRemoveBlank(paragraph, isStandardWidth);

            // 3. 根据alignment参数设置对齐方式
            ProcessAlignment(paragraph, isStandardWidth);
        }
    }

    /// <summary>
    /// 调整图片大小
    /// </summary>
    /// <param name="shape">图片形状对象</param>
    /// <returns>调整后是否为标准宽度</returns>
    private bool AdjustImageSize(Shape shape)
    {
        try
        {
            // 获取当前图片宽度（转换为厘米）
            double currentWidthCm = UnitConverter.PointsToCm(shape.Width);

            // 如果图片宽度大于或等于最小宽度，则调整为标准宽度
            if (currentWidthCm >= _minimumWidth)
            {
                // *** 优化后的简化逻辑：直接设置宽度，让Aspose.Words自动处理纵横比 ***

                // 将图片宽度设置为标准宽度（以磅为单位）
                // 如果图片锁定了纵横比，Aspose.Words会自动按比例调整高度
                shape.Width = UnitConverter.CmToPoints(_standardWidth);

                return true; // 返回true表示调整后为标准宽度
            }

            return false; // 返回false表示未调整或调整后不是标准宽度
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 处理空格删除
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <param name="isStandardWidth">是否为标准宽度</param>
    private void ProcessRemoveBlank(Paragraph paragraph, bool isStandardWidth)
    {
        bool shouldRemoveBlank = false;

        switch (_removeBlank)
        {
            case "removeBlank.all":
                shouldRemoveBlank = true;
                break;
            case "removeBlank.standard":
                shouldRemoveBlank = isStandardWidth;
                break;
            case "removeBlank.off":
            default:
                shouldRemoveBlank = false;
                break;
        }

        if (shouldRemoveBlank)
        {
            RemoveSpacesFromParagraph(paragraph);
        }
    }

    /// <summary>
    /// 从段落中删除所有空格
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    private void RemoveSpacesFromParagraph(Paragraph paragraph)
    {
        try
        {
            // 获取段落中的所有Run节点
            var runs = paragraph.GetChildNodes(NodeType.Run, true).OfType<Run>().ToList();

            foreach (Run run in runs)
            {
                if (!string.IsNullOrEmpty(run.Text))
                {
                    // 删除所有空白字符
                    string cleanedText = Regex.Replace(run.Text, @"\s", "", RegexOptions.Compiled);
                    run.Text = cleanedText;
                }
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    /// <summary>
    /// 处理段落对齐方式
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <param name="isStandardWidth">是否为标准宽度</param>
    private void ProcessAlignment(Paragraph paragraph, bool isStandardWidth)
    {
        ParagraphAlignment? targetAlignment = null;

        switch (_alignment)
        {
            case "alignment.left.all":
                targetAlignment = ParagraphAlignment.Left;
                break;
            case "alignment.left.standard":
                if (isStandardWidth)
                    targetAlignment = ParagraphAlignment.Left;
                break;
            case "alignment.middle.all":
                targetAlignment = ParagraphAlignment.Center;
                break;
            case "alignment.middle.standard":
                if (isStandardWidth)
                    targetAlignment = ParagraphAlignment.Center;
                break;
            case "alignment.right.all":
                targetAlignment = ParagraphAlignment.Right;
                break;
            case "alignment.right.standard":
                if (isStandardWidth)
                    targetAlignment = ParagraphAlignment.Right;
                break;
            case "alignment.null":
            default:
                // 不设置对齐方式
                break;
        }

        if (targetAlignment.HasValue)
        {
            paragraph.ParagraphFormat.Alignment = targetAlignment.Value;
        }
    }
}
