using System;
using System.Drawing;
using System.Text;
using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 题库处理器 - 在题目答案后添加END标签
/// 实现IPipelineStep接口
/// </summary>
public class InsertEndTagStep : IPipelineStep
{
    // 题型定义
    public enum QuestionType
    {
        Unknown = 0,   // 未知类型
        Choice = 1,    // 选择题
        FillBlank = 2, // 填空题
        Comprehensive = 3, // 综合题(解答题/实验题/计算题等)
        TrueFalse = 4  // 判断题
    }
    
    // 日志记录
    private readonly List<string> _logs = new List<string>();
    
    // 处理统计
    private int _totalQuestions = 0;
    private readonly Dictionary<QuestionType, int> _typeStatistics = new Dictionary<QuestionType, int>
    {
        { QuestionType.Unknown, 0 },
        { QuestionType.Choice, 0 },
        { QuestionType.FillBlank, 0 },
        { QuestionType.Comprehensive, 0 },
        { QuestionType.TrueFalse, 0 }
    };
    
    // 存储题型标题字典，用于识别和跳过题型标题段落
    private readonly HashSet<string> _questionTypeHeaders;
    
    // 题型区间信息，用于基于题型标题的优先判断
    private readonly List<QuestionTypeSection> _questionTypeSections = new List<QuestionTypeSection>();
    
    // 字体配置
    private readonly string? _latinFont; // 西文字体配置，可为null
    private readonly string? _cjkFont;   // 中文字体配置，可为null
    
    // 图片跳过配置
    private readonly bool _forceSkipImages; // 是否强制跳过所有纯图片段落
    
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="targetFields">题型标题列表，用于识别需要跳过的题型标题段落</param>
    /// <param name="latinFont">西文字体名称，用于设置END标签的Font.Name，如果为null则不设置</param>
    /// <param name="cjkFont">中文字体名称，用于设置END标签的Font.NameFarEast，如果为null则不设置</param>
    /// <param name="forceSkipImages">是否强制跳过所有纯图片段落，false=智能跳过（图片前有空行才跳过），true=强制跳过所有纯图片段落</param>
    public InsertEndTagStep(List<string>? targetFields = null, string? latinFont = null, string? cjkFont = null, bool forceSkipImages = false)
    {
        // 将targetFields转换为HashSet，提高查找效率
        _questionTypeHeaders = targetFields != null
            ? new HashSet<string>(targetFields)
            : new HashSet<string>();
            
        _latinFont = latinFont;
        _cjkFont = cjkFont;
        _forceSkipImages = forceSkipImages;
    }
    
    /// <summary>
    /// 执行文档处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>是否处理成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            LogInfo($"开始处理文件: {filePath}");
            
            // 获取文档中所有段落（不在表格中的段落）
            List<Paragraph> paragraphs = GetNonTableParagraphs(doc);
            LogInfo($"文档共有 {paragraphs.Count} 个非表格段落");
            
            // 查找所有题目起始位置和答案位置
            Dictionary<int, QuestionInfo> questions = IdentifyQuestions(paragraphs);
            LogInfo($"共识别出 {questions.Count} 个题目");
            
            // 处理每个题目
            ProcessAllQuestions(paragraphs, questions);
            
            // 输出统计结果
            LogSummary();
            
            return true;
        }
        catch (Exception ex)
        {
            LogError($"处理出错: {ex.Message}\n{ex.StackTrace}");
            return false;
        }
    }
    
    /// <summary>
    /// 获取文档中的所有段落（包括表格内的段落）
    /// 注意：修复了之前错误排除表格段落的bug
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>段落列表</returns>
    private List<Paragraph> GetNonTableParagraphs(Document doc)
    {
        List<Paragraph> paragraphs = new List<Paragraph>();
        
        // 获取所有段落节点（包括表格内的段落）
        NodeCollection allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true);
        
        foreach (Paragraph para in allParagraphs)
        {
            // 跳过页眉页脚中的段落
            if (IsInHeaderFooter(para))
                continue;
                
            // 【修复】包含所有段落，不再排除表格内的段落
            // 原代码错误地排除了表格段落，这不符合用户预期
            // 表格内容也可能包含题目和答案，需要正常处理
            paragraphs.Add(para);
        }
        
        return paragraphs;
    }
    
    /// <summary>
    /// 检查段落是否在页眉页脚中
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否在页眉页脚中</returns>
    private bool IsInHeaderFooter(Paragraph para)
    {
        return para.GetAncestor(typeof(HeaderFooter)) != null;
    }
    
    /// <summary>
    /// 识别所有题目及其类型
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <returns>题目信息字典，键为题目起始段落索引</returns>
    private Dictionary<int, QuestionInfo> IdentifyQuestions(List<Paragraph> paragraphs)
    {
        Dictionary<int, QuestionInfo> questions = new Dictionary<int, QuestionInfo>();
        
        // 定义正则表达式匹配题目起始特征: 段落开头的数字+．
        // 支持更多题号格式: 数字+点、数字+．、数字+.
        Regex questionStartPattern = new Regex(@"^\s*\d+[\．]\s*", RegexOptions.Compiled);
        
        // 遍历所有段落查找题目
        QuestionInfo currentQuestion = null;
        int answerStartIndex = -1;
        
        // 首先识别题型标题并建立区间映射
        IdentifyQuestionTypeSections(paragraphs);
        
        for (int i = 0; i < paragraphs.Count; i++)
        {
            string text = paragraphs[i].ToString(SaveFormat.Text).Trim();
            
            // 如果遇到题型标题，检查并记录下一个题型类别
            if (text.Contains("一、") || text.Contains("二、") || text.Contains("三、") || 
                text.Contains("四、") || text.Contains("五、") || text.Contains("六、") ||
                text.Contains("I.") || text.Contains("II.") || text.Contains("III.") ||
                text.Contains("IV.") || text.Contains("V.") || text.Contains("VI."))
            {
                // 检查是否为题型标识
                QuestionType sectionType = DetectSectionType(text);
                if (sectionType != QuestionType.Unknown)
                {
                    LogInfo($"检测到题型标题: {text}, 类型: {GetTypeName(sectionType)}");
                }
            }
            
            // 检查是否为题目开始
            if (questionStartPattern.IsMatch(text))
            {
                // 如果有正在处理的题目，确定其结束位置
                if (currentQuestion != null && answerStartIndex >= 0)
                {
                    currentQuestion.AnswerEndIndex = i - 1;
                }
                
                // 创建新题目
                currentQuestion = new QuestionInfo
                {
                    QuestionStartIndex = i,
                    AnswerStartIndex = -1,
                    AnswerEndIndex = -1,
                    Type = DetectQuestionType(paragraphs, i)
                };
                
                questions[i] = currentQuestion;
                answerStartIndex = -1;
                _totalQuestions++;
                
                LogInfo($"题目 {_totalQuestions}: 在段落 {i} 处开始，类型: {GetTypeName(currentQuestion.Type)}");
                _typeStatistics[currentQuestion.Type]++;
            }
            
            // 检查是否为答案部分
            if (text.Contains("【答案】") && currentQuestion != null && currentQuestion.AnswerStartIndex < 0)
            {
                currentQuestion.AnswerStartIndex = i;
                answerStartIndex = i;
                LogInfo($"题目 {_totalQuestions}: 答案在段落 {i} 处开始");
                
                // 确认题型 - 仅在非题型区间判断时使用答案内容验证
                // 如果是基于题型标题区间判断的，则优先保持题型区间的判断结果
                QuestionType sectionBasedType = GetQuestionTypeBySection(currentQuestion.QuestionStartIndex);
                
                if (sectionBasedType == QuestionType.Unknown)
                {
                    // 只有在没有题型区间信息时，才通过答案内容进行二次确认
                    string fullAnswerText = text;
                    int answerEndScan = Math.Min(i + 5, paragraphs.Count - 1); // 向下最多看5个段落
                    
                    for (int j = i + 1; j <= answerEndScan; j++)
                    {
                        string nextParaText = paragraphs[j].ToString(SaveFormat.Text).Trim();
                        if (questionStartPattern.IsMatch(nextParaText)) 
                            break; // 遇到下一题则停止
                        
                        fullAnswerText += " " + nextParaText;
                    }
                    
                    QuestionType confirmedType = ConfirmTypeByAnswer(fullAnswerText, currentQuestion);
                    
                    if (confirmedType != currentQuestion.Type)
                    {
                        LogInfo($"题目 {_totalQuestions}: 通过答案调整类型为 {GetTypeName(confirmedType)}");
                        
                        // 更新统计
                        _typeStatistics[currentQuestion.Type]--;
                        _typeStatistics[confirmedType]++;
                        
                        currentQuestion.Type = confirmedType;
                    }
                }
                else
                {
                    LogInfo($"题目 {_totalQuestions}: 基于题型区间判断，保持类型为 {GetTypeName(currentQuestion.Type)}");
                }
            }
        }
        
        // 处理最后一个题目
        if (currentQuestion != null && answerStartIndex >= 0)
        {
            currentQuestion.AnswerEndIndex = paragraphs.Count - 1;
        }
        
        return questions;
    }
    
    /// <summary>
    /// 识别题型标题并建立区间映射
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    private void IdentifyQuestionTypeSections(List<Paragraph> paragraphs)
    {
        _questionTypeSections.Clear();

        // 正则表达式：匹配题型标题格式（大写中文数字+顿号+XXX题+可选的额外信息）
        Regex sectionTitlePattern = new Regex(@"^[一二三四五六七八九十]+、\S*题", RegexOptions.Compiled);

        for (int i = 0; i < paragraphs.Count; i++)
        {
            string text = paragraphs[i].ToString(SaveFormat.Text).Trim();

            // 检查是否为题型标题：格式匹配且段落只包含题型文本
            if (sectionTitlePattern.IsMatch(text))
            {
                QuestionType sectionType = ParseQuestionTypeFromTitle(text);

                if (sectionType != QuestionType.Unknown)
                {
                    var section = new QuestionTypeSection
                    {
                        StartIndex = i,
                        EndIndex = paragraphs.Count - 1, // 先设为文档末尾，后面会更新
                        Type = sectionType,
                        Title = text
                    };

                    // 【修复】更新前一个区间的结束位置 - 修正边界设置
                    if (_questionTypeSections.Count > 0)
                    {
                        // 将前一个区间的结束位置设置为当前题型标题的前一个段落
                        // 但要确保不会产生无效区间（StartIndex >= EndIndex）
                        int previousEndIndex = i - 1;
                        var previousSection = _questionTypeSections[_questionTypeSections.Count - 1];

                        // 确保前一个区间至少包含其标题段落
                        if (previousEndIndex > previousSection.StartIndex)
                        {
                            previousSection.EndIndex = previousEndIndex;
                            LogInfo($"更新前一个题型区间结束位置: {previousSection.Title} -> 结束段落: {previousEndIndex}");
                        }
                        else
                        {
                            // 如果计算出的结束位置无效，则设置为标题段落的下一个段落
                            previousSection.EndIndex = previousSection.StartIndex + 1;
                            LogInfo($"前一个题型区间边界修正: {previousSection.Title} -> 结束段落: {previousSection.EndIndex}");
                        }
                    }

                    _questionTypeSections.Add(section);
                    LogInfo($"识别题型区间: {text} -> {GetTypeName(sectionType)}, 起始段落: {i}");
                }
            }
        }

        LogInfo($"共识别出 {_questionTypeSections.Count} 个题型区间");

        // 【新增】输出所有区间的详细信息用于调试
        for (int i = 0; i < _questionTypeSections.Count; i++)
        {
            var section = _questionTypeSections[i];
            LogInfo($"题型区间 {i + 1}: {section.Title} ({GetTypeName(section.Type)}) - 段落范围: {section.StartIndex} 到 {section.EndIndex}");
        }
    }
    
    /// <summary>
    /// 从题型标题解析题目类型
    /// </summary>
    /// <param name="title">题型标题</param>
    /// <returns>题目类型</returns>
    private QuestionType ParseQuestionTypeFromTitle(string title)
    {
        // 单选题、多选题、选择题 → 【END1】
        if (title.Contains("单选题") || title.Contains("多选题") || title.Contains("选择题"))
        {
            return QuestionType.Choice;
        }
        
        // 填空题 → 【END2】
        if (title.Contains("填空题"))
        {
            return QuestionType.FillBlank;
        }
        
        // 判断题 → 【END4】
        if (title.Contains("判断题"))
        {
            return QuestionType.TrueFalse;
        }
        
        // 其他题型 → 【END3】
        if (title.Contains("解答题") || title.Contains("计算题") || title.Contains("实验题") ||
            title.Contains("作图题") || title.Contains("综合题") || title.Contains("应用题") ||
            title.Contains("科普阅读题") || title.Contains("填空与简答题") || title.Contains("综合应用题") ||
            title.Contains("科学探究题") || title.EndsWith("题"))
        {
            return QuestionType.Comprehensive;
        }
        
        return QuestionType.Unknown;
    }
    
    /// <summary>
    /// 根据题目段落索引查找所属题型区间
    /// </summary>
    /// <param name="questionIndex">题目段落索引</param>
    /// <returns>题目类型，如果未找到返回Unknown</returns>
    private QuestionType GetQuestionTypeBySection(int questionIndex)
    {
        foreach (var section in _questionTypeSections)
        {
            // 【修复】修正区间判断逻辑：
            // 题目应该在题型标题之后，但在下一个题型标题之前（包含边界）
            // 原逻辑：questionIndex > section.StartIndex && questionIndex <= section.EndIndex
            // 新逻辑：questionIndex > section.StartIndex && questionIndex <= section.EndIndex
            // 但要确保边界处理正确
            if (questionIndex > section.StartIndex && questionIndex <= section.EndIndex)
            {
                LogInfo($"题目在段落 {questionIndex} 属于题型区间: {section.Title} -> {GetTypeName(section.Type)} (区间: {section.StartIndex}-{section.EndIndex})");
                return section.Type;
            }
        }

        // 【新增】如果没有找到匹配的区间，输出调试信息
        LogInfo($"题目在段落 {questionIndex} 未找到匹配的题型区间");
        LogInfo($"当前所有题型区间:");
        foreach (var section in _questionTypeSections)
        {
            LogInfo($"  - {section.Title} ({GetTypeName(section.Type)}): 段落 {section.StartIndex} 到 {section.EndIndex}");
        }

        return QuestionType.Unknown;
    }
    
    /// <summary>
    /// 处理所有题目
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="questions">题目信息</param>
    private void ProcessAllQuestions(List<Paragraph> paragraphs, Dictionary<int, QuestionInfo> questions)
    {
        // 先按题目起始位置排序
        List<int> sortedStarts = new List<int>(questions.Keys);
        sortedStarts.Sort();

        // 检查文档是否存在题型标题区间
        bool hasQuestionTypeSections = _questionTypeSections.Count > 0;
        LogInfo($"文档是否存在题型标题区间: {hasQuestionTypeSections}");

        // ============================================ 第一优先级：选择题检测 ============================================
        // 通过答案格式精确识别选择题，并直接插入【END1】标签
        LogInfo("开始第一优先级选择题检测...");
        int choiceQuestionsProcessed = ProcessChoiceQuestionsByAnswer(paragraphs, questions, sortedStarts);
        LogInfo($"第一优先级选择题检测完成，共处理 {choiceQuestionsProcessed} 道选择题");

        // ============================================ 第二优先级：基于题型标题区间的判断 ============================================
        // 处理基于题型标题（如"二、填空题"）的题目类型判断
        LogInfo("开始第二优先级基于题型标题区间的判断...");
        int sectionBasedQuestionsProcessed = ProcessQuestionsBySection(paragraphs, questions, sortedStarts);
        LogInfo($"第二优先级基于题型标题区间判断完成，共处理 {sectionBasedQuestionsProcessed} 道题目");

        // ============================================ 第三优先级：其他题型检测 ============================================
        // 根据是否存在题型标题区间决定处理策略
        // 如果文档有题型标题区间，则不再通过内容特征识别选择题
        // 如果文档没有题型标题区间，则使用原有的深入分析逻辑（包括选择题内容特征识别）
        LogInfo("开始第三优先级其他题型检测...");
        int otherQuestionsProcessed = ProcessOtherQuestions(paragraphs, questions, sortedStarts, hasQuestionTypeSections);
        LogInfo($"第三优先级其他题型检测完成，共处理 {otherQuestionsProcessed} 道题目");
    }
    
    /// <summary>
    /// 第一优先级：通过答案格式精确识别选择题并插入【END1】标签
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="questions">题目信息字典</param>
    /// <param name="sortedStarts">排序后的题目起始位置列表</param>
    /// <returns>处理的选择题数量</returns>
    private int ProcessChoiceQuestionsByAnswer(List<Paragraph> paragraphs, Dictionary<int, QuestionInfo> questions, List<int> sortedStarts)
    {
        int processedCount = 0;

        for (int i = 0; i < sortedStarts.Count; i++)
        {
            int start = sortedStarts[i];
            QuestionInfo question = questions[start];

            // 跳过没有答案的题目
            if (question.AnswerStartIndex < 0)
            {
                continue;
            }

            // 确定答案结束位置
            if (i < sortedStarts.Count - 1)
            {
                int nextStart = sortedStarts[i + 1];
                question.AnswerEndIndex = nextStart - 1;
            }
            else
            {
                question.AnswerEndIndex = paragraphs.Count - 1;
            }

            // 检查是否为选择题（通过答案格式判断）
            if (IsChoiceQuestionByAnswer(paragraphs, question))
            {
                // 强制设置为选择题类型
                if (question.Type != QuestionType.Choice)
                {
                    // 更新统计
                    _typeStatistics[question.Type]--;
                    _typeStatistics[QuestionType.Choice]++;
                    question.Type = QuestionType.Choice;
                    LogInfo($"题目 {processedCount + 1}: 通过答案格式确认为选择题，类型已调整");
                }

                // 直接插入【END1】标签
                InsertEndTag(paragraphs, question);
                question.IsProcessed = true;
                processedCount++;

                LogInfo($"选择题 {processedCount}: 在段落 {start} 处插入【END1】标签");
            }
        }

        return processedCount;
    }

    /// <summary>
    /// 第二优先级：基于题型标题区间的判断处理题目
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="questions">题目信息字典</param>
    /// <param name="sortedStarts">排序后的题目起始位置列表</param>
    /// <returns>处理的题目数量</returns>
    private int ProcessQuestionsBySection(List<Paragraph> paragraphs, Dictionary<int, QuestionInfo> questions, List<int> sortedStarts)
    {
        int processedCount = 0;

        for (int i = 0; i < sortedStarts.Count; i++)
        {
            int start = sortedStarts[i];
            QuestionInfo question = questions[start];

            // 跳过已处理的题目或没有答案的题目
            if (question.IsProcessed || question.AnswerStartIndex < 0)
            {
                continue;
            }

            // 确定答案结束位置
            if (i < sortedStarts.Count - 1)
            {
                int nextStart = sortedStarts[i + 1];
                question.AnswerEndIndex = nextStart - 1;
            }
            else
            {
                question.AnswerEndIndex = paragraphs.Count - 1;
            }

            // 检查题目是否已经通过区间判断确定了类型（在初始识别时已经确定）
            // 如果题目类型不是Unknown，说明已经通过区间判断确定了类型
            if (question.Type != QuestionType.Unknown)
            {
                // 插入对应的END标签
                InsertEndTag(paragraphs, question);
                question.IsProcessed = true;
                processedCount++;

                LogInfo($"基于题型标题区间处理题目 {processedCount}: 在段落 {start} 处插入 {GetEndTag(question.Type)} 标签，类型: {GetTypeName(question.Type)}");
            }
        }

        return processedCount;
    }

    /// <summary>
    /// 第三优先级：根据是否存在题型标题区间决定处理策略
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="questions">题目信息字典</param>
    /// <param name="sortedStarts">排序后的题目起始位置列表</param>
    /// <param name="hasQuestionTypeSections">是否存在题型标题区间</param>
    /// <returns>处理的题目数量</returns>
    private int ProcessOtherQuestions(List<Paragraph> paragraphs, Dictionary<int, QuestionInfo> questions, List<int> sortedStarts, bool hasQuestionTypeSections)
    {
        int processedCount = 0;

        for (int i = 0; i < sortedStarts.Count; i++)
        {
            int start = sortedStarts[i];
            QuestionInfo question = questions[start];

            // 跳过已处理的题目或没有答案的题目
            if (question.IsProcessed || question.AnswerStartIndex < 0)
            {
                continue;
            }

            // 确定答案结束位置
            if (i < sortedStarts.Count - 1)
            {
                int nextStart = sortedStarts[i + 1];
                question.AnswerEndIndex = nextStart - 1;
            }
            else
            {
                question.AnswerEndIndex = paragraphs.Count - 1;
            }

            if (hasQuestionTypeSections)
            {
                // 【修复】有题型标题区间时：完全依赖区间判断，严格禁用任何内容分析
                // 题目类型已经在初始识别时通过区间判断确定，这里不需要再次判断
                LogInfo($"题目 {processedCount + 1}: 有题型标题区间，完全依赖区间判断，类型为 {GetTypeName(question.Type)}");

                // 【关键修复】在有题型标题区间的情况下，绝对不允许修改题目类型
                // 这是导致填空题被错误标记为选择题的根本原因
                // 直接跳过所有内容分析，保持原有类型不变
            }
            else
            {
                // 没有题型标题区间时：使用原有的深入分析逻辑（包括选择题内容特征识别）
                LogInfo($"题目 {processedCount + 1}: 无题型标题区间，使用内容特征分析");

                // 收集题目的上下文进行选择题检测
                StringBuilder contextBuilder = new StringBuilder();
                int maxParas = Math.Min(paragraphs.Count, question.QuestionStartIndex + 10);

                for (int j = question.QuestionStartIndex; j < maxParas; j++)
                {
                    string text = paragraphs[j].ToString(SaveFormat.Text).Trim();

                    // 如果遇到答案或下一题则停止
                    if (text.Contains("【答案】") || (j > question.QuestionStartIndex && Regex.IsMatch(text, @"^\d+[\.．。]")))
                    {
                        break;
                    }

                    contextBuilder.Append(" ").Append(text);
                }

                string context = contextBuilder.ToString();

                // 检查是否为选择题（通过题目内容特征判断）
                if (IsChoiceQuestion(context))
                {
                    // 强制设置为选择题类型
                    if (question.Type != QuestionType.Choice)
                    {
                        // 更新统计
                        _typeStatistics[question.Type]--;
                        _typeStatistics[QuestionType.Choice]++;
                        question.Type = QuestionType.Choice;
                        LogInfo($"题目 {processedCount + 1}: 通过题目内容特征确认为选择题，类型已调整");
                    }
                }
                else
                {
                    // 不是选择题时，使用原有的深入分析逻辑
                    question.Type = DetectQuestionTypeWithoutSection(paragraphs, question.QuestionStartIndex);
                }
            }

            // 处理当前题目
            InsertEndTag(paragraphs, question);
            question.IsProcessed = true;
            processedCount++;

            string questionTypeDesc = hasQuestionTypeSections ? "简化逻辑" : "深入分析";
            LogInfo($"{questionTypeDesc}处理题目 {processedCount}: 在段落 {start} 处插入 {GetEndTag(question.Type)} 标签");
        }

        return processedCount;
    }



    /// <summary>
    /// 通过答案格式判断是否为选择题
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="question">题目信息</param>
    /// <returns>是否为选择题</returns>
    private bool IsChoiceQuestionByAnswer(List<Paragraph> paragraphs, QuestionInfo question)
    {
        // 收集答案段落的文本内容
        StringBuilder answerTextBuilder = new StringBuilder();
        
        for (int i = question.AnswerStartIndex; i <= question.AnswerEndIndex; i++)
        {
            if (i < paragraphs.Count)
            {
                string paraText = paragraphs[i].ToString(SaveFormat.Text).Trim();
                answerTextBuilder.Append(" ").Append(paraText);
            }
        }
        
        string answerText = answerTextBuilder.ToString();
        
        // 使用正则表达式精确匹配选择题答案格式
        // 单选题：【答案】A 或 【答案】B 等（一个大写字母）
        // 多选题：【答案】AB 或 【答案】ABC 等（两个或以上大写字母）
        var singleChoicePattern = new Regex(@"【答案】\s*[A-Z]\s*(?:$|[。．\s])", RegexOptions.Compiled);
        var multipleChoicePattern = new Regex(@"【答案】\s*[A-Z]{2,}\s*(?:$|[。．\s])", RegexOptions.Compiled);
        
        // 检查是否匹配单选题或多选题格式
        bool isSingleChoice = singleChoicePattern.IsMatch(answerText);
        bool isMultipleChoice = multipleChoicePattern.IsMatch(answerText);
        
        if (isSingleChoice || isMultipleChoice)
        {
            string choiceType = isSingleChoice ? "单选题" : "多选题";
            LogInfo($"通过答案格式识别为{choiceType}，答案内容: {answerText.Substring(0, Math.Min(50, answerText.Length))}...");
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// 在答案后插入END标签
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="question">题目信息</param>
    private void InsertEndTag(List<Paragraph> paragraphs, QuestionInfo question)
    {
        if (question.AnswerStartIndex < 0 || question.AnswerEndIndex < question.AnswerStartIndex)
        {
            LogWarning($"题目答案段落索引无效: {question.AnswerStartIndex} - {question.AnswerEndIndex}，尝试在题目后插入END标签");
            
            // 即使答案索引无效，也尝试在题目段落后插入END标签和空行
            try
            {
                int questionStartIndex = question.QuestionStartIndex;
                if (questionStartIndex >= 0 && questionStartIndex < paragraphs.Count)
                {
                    Paragraph questionPara = paragraphs[questionStartIndex];
                    Document doc = (Document)questionPara.GetAncestor(NodeType.Document) ?? (Document)questionPara.Document;
                    
                    // 创建END标签段落
                    Paragraph endTagPara = (Paragraph)questionPara.Clone(true);
                    while (endTagPara.HasChildNodes)
                    {
                        endTagPara.RemoveChild(endTagPara.FirstChild);
                    }
                    
                    // 创建END标签的Run
                    Run endTagRun = new Run(doc);
                    endTagRun.Text = GetEndTag(question.Type);
                    ApplyFontSettings(endTagRun);
                    endTagPara.AppendChild(endTagRun);
                    endTagPara.ParagraphFormat.Alignment = ParagraphAlignment.Left;
                    
                    // 插入END标签段落
                    questionPara.ParentNode.InsertAfter(endTagPara, questionPara);
                    
                    // 插入空行
                    InsertEmptyLineAfterEndTag(endTagPara, false); // false表示无条件插入空行
                    
                    // 【关键修复】检查END标签后是否紧跟图片段落，确保它们之间有一个空行
                    EnsureEmptyLineBeforeImageParagraph(endTagPara);
                    
                    // 【新增优化】检查END标签是否在文档末尾，如果是则清理末尾多余空行
                    try
                    {
                        LogInfo("开始检查END标签是否在文档末尾...");
                        CleanupTrailingEmptyLinesIfAtDocumentEnd(doc, endTagPara);
                    }
                    catch (Exception trailingEx)
                    {
                        LogError($"清理文档末尾空行时出错: {trailingEx.Message}");
                    }
                    
                    LogInfo($"在无效答案索引情况下，在题目段落 {questionStartIndex} 后插入了END标签和空行");
                }
            }
            catch (Exception ex)
            {
                LogError($"在无效答案索引情况下插入END标签出错: {ex.Message}");
            }
            return;
        }
        
        try
        {
            // 查找答案的最后一个非空段落
            int lastNonEmptyParaIndex = question.AnswerEndIndex;
            
            // 从答案结束往前找到最后一个非空段落（包含图片、公式等重要内容的段落也算非空）
            for (int i = question.AnswerEndIndex; i >= question.AnswerStartIndex; i--)
            {
                string paraText = paragraphs[i].ToString(SaveFormat.Text).Trim();
                Paragraph para = paragraphs[i];
                
                // 检查是否为非空段落：有文字内容 或 包含重要内容（图片、公式、表格等）
                bool hasText = !string.IsNullOrWhiteSpace(paraText);
                bool hasImportantContent = HasImageOrShape(para) || HasOfficeMath(para) || HasTable(para) || HasOtherImportantContent(para);
                
                if (hasText || hasImportantContent)
                {
                    lastNonEmptyParaIndex = i;
                    break;
                }
            }
            
            // ==================== 智能跳过不需要的段落 ====================
            var skippedElements = new List<(int index, string type, string content)>(); // 记录跳过的元素
            
            // 智能向前查找真正的答案结束位置，跳过标题、题型标题、换行符、分页符等
            lastNonEmptyParaIndex = FindValidAnswerEndPosition(paragraphs, lastNonEmptyParaIndex, question.AnswerStartIndex, skippedElements);
            
            // 获取答案的最后一个有效段落
            Paragraph lastAnswerPara = paragraphs[lastNonEmptyParaIndex];
            
            // 输出跳过的元素信息
            if (skippedElements.Count > 0)
            {
                LogInfo($"跳过了 {skippedElements.Count} 个元素:");
                foreach (var (index, type, content) in skippedElements)
                {
                    LogInfo($"  - 段落 {index}: {type} - '{content.Substring(0, Math.Min(30, content.Length))}...'");
                }
            }
            
            // 【新增】检查是否需要特殊处理表格
            if (HasTable(lastAnswerPara))
            {
                LogInfo("最后有效段落包含表格，将在表格后插入空段落和END标签");
                lastAnswerPara = HandleTableEndInsertion(lastAnswerPara);
            }
            
            // 获取文档对象
            Document doc = (Document)lastAnswerPara.GetAncestor(NodeType.Document);
            if (doc == null)
            {
                // 备用方法
                doc = (Document)lastAnswerPara.Document;
            }
            
            // 创建END标签段落，复制原段落以保持格式
            Paragraph endTagPara = (Paragraph)lastAnswerPara.Clone(true);
            
            // 清除段落内容 - 使用安全方式
            while (endTagPara.HasChildNodes)
            {
                endTagPara.RemoveChild(endTagPara.FirstChild);
            }
            
            // 创建END标签的Run对象
            Run endTagRun = null;
            
            // 如果最后一个段落有内容，复制其格式
            if (lastAnswerPara.HasChildNodes)
            {
                // 查找最后一个Run
                Run lastRun = null;
                NodeCollection childNodes = lastAnswerPara.GetChildNodes(NodeType.Run, false);
                
                if (childNodes.Count > 0)
                {
                    lastRun = (Run)childNodes[childNodes.Count - 1];
                }
                
                if (lastRun != null)
                {
                    endTagRun = (Run)lastRun.Clone(true);
                    endTagRun.Text = GetEndTag(question.Type);
                    
                    // 设置为非斜体并应用字体设置
                    if (endTagRun.Font != null)
                    {
                        endTagRun.Font.Italic = false;
                        endTagRun.Font.Bold = false;
                        endTagRun.Font.Subscript = false;
                        endTagRun.Font.Superscript = false; // 明确设置为非上标
                        
                        // 应用字体设置
                        ApplyFontSettings(endTagRun);
                    }
                }
            }
            
            // 如果没有找到Run对象，则创建新的
            if (endTagRun == null)
            {
                endTagRun = new Run(doc);
                endTagRun.Text = GetEndTag(question.Type);
                
                // 应用字体设置
                ApplyFontSettings(endTagRun);
            }
            
            // 添加Run到段落
            endTagPara.AppendChild(endTagRun);
            
            
            // +++ 新增代码：强制设置左对齐 +++
            // 获取段落格式并设置对齐方式（在插入文档前设置）
            endTagPara.ParagraphFormat.Alignment = ParagraphAlignment.Left;
            
            // 在最后一个有效答案段落后插入END标签段落
            lastAnswerPara.ParentNode.InsertAfter(endTagPara, lastAnswerPara);
            
            
            // 插入空行逻辑（根据跳过元素决定是否插入）
            bool hasSkippedTitle = skippedElements.Any(element => element.type.Contains("标题"));
            InsertEmptyLineAfterEndTag(endTagPara, hasSkippedTitle);
    
            // 【关键修复】检查END标签后是否紧跟图片段落，确保它们之间有一个空行
            EnsureEmptyLineBeforeImageParagraph(endTagPara);
            
            // 【重要修复】完全移除删除逻辑！跳过不等于删除！
            // 我们只需要找到正确的插入位置，不应该删除任何被"跳过"的段落
            // CleanupSkippedElements(paragraphs, lastNonEmptyParaIndex, question.AnswerEndIndex, skippedElements);
            LogInfo("跳过段落不进行删除，保持文档完整性");
            
            LogInfo($"在段落 {lastNonEmptyParaIndex} 后插入了 {GetEndTag(question.Type)} 标签");
            
            // 【新增】检查是否遇到表格，如果遇到表格则删除END标签前面一行空行
            bool hasEncounteredTable = skippedElements.Any(element => element.type.Contains("表格"));
            if (hasEncounteredTable)
            {
                try
                {
                    // 查找END标签前面的段落，如果是空行则删除
                    var currentParagraphs = GetNonTableParagraphs(doc);
                    string endTagText = GetEndTag(question.Type);
                    
                    // 查找END标签段落
                    for (int i = Math.Max(0, lastNonEmptyParaIndex - 2); i < Math.Min(currentParagraphs.Count, lastNonEmptyParaIndex + 5); i++)
                    {
                        string paraText = currentParagraphs[i].ToString(SaveFormat.Text).Trim();
                        if (paraText.Contains(endTagText))
                        {
                            // 找到END标签段落，检查前面一个段落
                            if (i > 0)
                            {
                                var prevPara = currentParagraphs[i - 1];
                                string prevText = prevPara.ToString(SaveFormat.Text).Trim();
                                if (string.IsNullOrWhiteSpace(prevText) && 
                                    !HasImageOrShape(prevPara) && !HasOfficeMath(prevPara) && !HasTable(prevPara) && !HasOtherImportantContent(prevPara))
                                {
                                    prevPara.Remove();
                                    LogInfo($"遇到表格，删除了END标签前面的空行：段落 {i-1}");
                                }
                            }
                            break;
                        }
                    }
                }
                catch (Exception tableEx)
                {
                    LogError($"处理表格时删除END标签前空行出错: {tableEx.Message}");
                }
            }
            
            // 【说明】空行已在第856行插入，无需重复插入
            // 【新增】检查并清理END标签后的多余空行，保留最多一行空行
            try
            {
                // 使用更直接的方法：直接从文档重新获取所有段落（包括表格内段落）
                var allParagraphs = new List<Paragraph>();
                foreach (Section section in doc.Sections)
                {
                    foreach (Node node in section.Body.GetChildNodes(NodeType.Paragraph, true))
                    {
                        if (node is Paragraph para)
                        {
                            // 简单排除页眉页脚段落
                            if (para.ParentNode != null && 
                                para.ParentNode.NodeType != NodeType.HeaderFooter)
                            {
                                allParagraphs.Add(para);
                            }
                        }
                    }
                }
                
                LogInfo($"重新获取段落列表，共{allParagraphs.Count}个段落（包括表格内段落）");
                
                // 查找END标签段落
                string endTagText = GetEndTag(question.Type);
                int endTagParagraphIndex = -1;
                
                // 扩大查找范围，从原位置前后各10个段落查找
                int searchStart = Math.Max(0, lastNonEmptyParaIndex - 5);
                int searchEnd = Math.Min(allParagraphs.Count - 1, lastNonEmptyParaIndex + 15);
                
                for (int i = searchStart; i <= searchEnd; i++)
                {
                    string paraText = allParagraphs[i].ToString(SaveFormat.Text).Trim();
                    if (paraText.Contains(endTagText))
                    {
                        endTagParagraphIndex = i;
                        LogInfo($"找到END标签段落：段落 {i} - '{paraText}'");
                        break;
                    }
                }
                
                if (endTagParagraphIndex >= 0)
                {
                    // 设置合理的结束索引：不超过段落总数
                    int cleanupEndIndex = Math.Min(allParagraphs.Count - 1, endTagParagraphIndex + 20);
                    LogInfo($"开始清理END标签后的空行，从段落{endTagParagraphIndex + 1}到段落{cleanupEndIndex}");
                    CleanupExcessiveEmptyLinesAfterEndTag(allParagraphs, endTagParagraphIndex, cleanupEndIndex);
                }
                else
                {
                    LogWarning($"未找到END标签段落，标签文本：'{endTagText}'，查找范围：{searchStart}-{searchEnd}");
                }
            }
            catch (Exception cleanupEx)
            {
                LogError($"清理END标签后空行时出错: {cleanupEx.Message}");
            }
            
            // 【新增优化】检查END标签是否在文档末尾，如果是则清理末尾多余空行
            try
            {
                LogInfo("开始检查END标签是否在文档末尾...");
                CleanupTrailingEmptyLinesIfAtDocumentEnd(doc, endTagPara);
            }
            catch (Exception trailingEx)
            {
                LogError($"清理文档末尾空行时出错: {trailingEx.Message}");
            }
        }
        catch (Exception ex)
        {
            LogError($"插入END标签时出错: {ex.Message}");
        }
    }
    

    /// <summary>
    /// 清理END标签后的多余空行，保留最多一行空行
    /// 注意：在调用此方法前，已经在END标签后插入了一个空行，所以现在检查是否有多余的空行
    /// 空行定义：只有纯文本为空（最多包含空格）的段落
    /// 遇到任何实际内容（图片、表格、公式、普通文本等）就停止检查
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="endTagIndex">END标签所在段落的索引</param>
    /// <param name="questionEndIndex">题目结束段落的索引</param>
    private void CleanupExcessiveEmptyLinesAfterEndTag(List<Paragraph> paragraphs, int endTagIndex, int questionEndIndex)
    {
        LogInfo($"开始空行清理：endTagIndex={endTagIndex}, questionEndIndex={questionEndIndex}, 总段落数={paragraphs.Count}");
        
        if (endTagIndex >= paragraphs.Count || endTagIndex >= questionEndIndex)
        {
            LogInfo($"跳过清理：索引超出范围");
            return;
        }
            
        List<int> emptyLineIndices = new List<int>();
        
        // 从END标签后面的段落开始检查，直到遇到第一个实际内容
        for (int i = endTagIndex + 1; i <= questionEndIndex && i < paragraphs.Count; i++)
        {
            Paragraph para = paragraphs[i];
            string paraText = para.ToString(SaveFormat.Text).Trim();
            
            LogInfo($"检查段落 {i}：文本='{paraText.Substring(0, Math.Min(50, paraText.Length))}...'");
            
            // 检查是否为纯空行（只有纯文本为空才算空行）
            if (string.IsNullOrWhiteSpace(paraText))
            {
                // 进一步检查是否包含任何实际内容（图片、公式、表格、分页符等）
                bool hasImage = HasImageOrShape(para);
                bool hasMath = HasOfficeMath(para);
                bool hasTable = HasTable(para);
                bool hasOther = HasOtherImportantContent(para);
                bool hasPageBreak = HasSectionBreakOrPageBreak(para);
                bool hasSectionBreak = HasSectionBreak(para);
                
                LogInfo($"段落 {i} 内容检查：hasImage={hasImage}, hasMath={hasMath}, hasTable={hasTable}, hasOther={hasOther}, hasPageBreak={hasPageBreak}, hasSectionBreak={hasSectionBreak}");
                
                if (hasImage || hasMath || hasTable || hasOther || hasPageBreak || hasSectionBreak)
                {
                    // 包含实际内容（包括分页符、分节符），不算空行，停止检查
                    LogInfo($"段落 {i} 包含实际内容（包括分页符、分节符），停止空行清理");
                    break;
                }
                else
                {
                    // 这是真正的空行
                    emptyLineIndices.Add(i);
                    LogInfo($"发现空行：段落 {i}");
                }
            }
            else
            {
                // 遇到有文本内容的段落，停止检查
                LogInfo($"遇到文本内容：段落 {i} = '{paraText.Substring(0, Math.Min(30, paraText.Length))}...'，停止空行清理");
                break;
            }
        }
        
        // 清理多余的空行，保留最多一行
        int removedCount = 0;
        if (emptyLineIndices.Count > 1)
        {
            LogInfo($"发现 {emptyLineIndices.Count} 个连续空行，保留第一个，删除其余 {emptyLineIndices.Count - 1} 个");
            
            // 保留第一个空行，删除后面的空行
            // 从后往前删除，避免索引变化影响
            for (int j = emptyLineIndices.Count - 1; j >= 1; j--)
            {
                int indexToRemove = emptyLineIndices[j];
                if (indexToRemove < paragraphs.Count && paragraphs[indexToRemove].ParentNode != null)
                {
                    LogInfo($"删除多余空行：段落 {indexToRemove}");
                    paragraphs[indexToRemove].Remove();
                    removedCount++;
                }
            }
        }
        
        if (removedCount > 0)
        {
            LogInfo($"END标签后清理了 {removedCount} 个多余空行，现在保留1个空行");
        }
        else if (emptyLineIndices.Count == 1)
        {
            LogInfo("END标签后有1个空行，符合要求，无需清理");
        }
        else if (emptyLineIndices.Count == 0)
        {
            LogInfo("END标签后没有发现额外空行（已有一个预插入的空行）");
        }
    }
    
    /// <summary>
    /// 根据题型获取END标签
    /// </summary>
    /// <param name="type">题目类型</param>
    /// <returns>END标签文本</returns>
    private string GetEndTag(QuestionType type)
    {
        switch (type)
        {
            case QuestionType.Choice:
                return "【END1】";
            case QuestionType.FillBlank:
                return "【END2】";
            case QuestionType.Comprehensive:
                return "【END3】";
            case QuestionType.TrueFalse:
                return "【END4】";
            default:
                return "【END】";
        }
    }
    
    /// <summary>
    /// 应用字体设置到Run节点
    /// </summary>
    
    /// <summary>
    /// 在END标签后插入空行
    /// </summary>
    /// <param name="endTagPara">END标签段落</param>
    /// <param name="hasSkippedTitle">是否跳过了标题（如果是，则不插入空行）</param>
    private void InsertEmptyLineAfterEndTag(Paragraph endTagPara, bool hasSkippedTitle)
    {
        // ========================== 统一插入空行逻辑 ==========================
        // 无论任何情况，都先插入一个空行
        Paragraph oneEmptyPara = (Paragraph)endTagPara.Clone(true);

        // 保留Run节点仅清空文本
        // 获取所有Run节点（保留格式的关键）
        NodeCollection runs = oneEmptyPara.GetChildNodes(NodeType.Run, true);

        // 安全遍历所有Run节点
        foreach (Run run in runs.OfType<Run>())
        {
            // 保留所有字体格式，仅清空文本内容
            run.Text = string.Empty; 

            // 可选：清除特殊格式（根据需求决定）
            // run.Font.AllCaps = false;
            // run.Font.Hidden = false;
        }

        // 克隆段落格式并修改底纹
        ParagraphFormat format = oneEmptyPara.ParagraphFormat;

        // 清除底纹填充色（设置为透明）
        format.Shading.BackgroundPatternColor = Color.Empty; // 或 Color.Transparent

        // 清除底纹图案类型（设为无填充）
        format.Shading.Texture = TextureIndex.TextureNone;

        // 清除所有边框（可选）
        format.Borders.ClearFormatting();

        // 插入空段落到END标签段落后
        endTagPara.ParentNode.InsertAfter(oneEmptyPara, endTagPara);
        LogInfo("在END标签后插入了空行");
        
        // ========================== 特殊处理：如果跳过了标题，删除一个空行 ==========================            
        if (hasSkippedTitle)
        {
            // 删除END标签后的第一个空行（就是刚刚插入的那个）
            if (oneEmptyPara.ParentNode != null)
            {
                oneEmptyPara.Remove();
                LogInfo("由于跳过了标题，删除了END标签后的空行");
            }
        }
    }

    /// <param name="run">要应用字体设置的Run节点</param>
    private void ApplyFontSettings(Run run)
    {
        if (run?.Font == null)
            return;
            
        // 应用西文字体设置
        if (!string.IsNullOrEmpty(_latinFont))
        {
            run.Font.Name = _latinFont;
        }
        
        // 应用中文字体设置
        if (!string.IsNullOrEmpty(_cjkFont))
        {
            run.Font.NameFarEast = _cjkFont;
        }
    }
    
    /// <summary>
    /// 检测题目类型
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="questionIndex">题目起始段落索引</param>
    /// <returns>题目类型</returns>
    private QuestionType DetectQuestionType(List<Paragraph> paragraphs, int questionIndex)
    {
        // ========== 优先级最高：基于题型标题区间的判断 ==========
        QuestionType sectionType = GetQuestionTypeBySection(questionIndex);
        if (sectionType != QuestionType.Unknown)
        {
            LogInfo($"题目在段落 {questionIndex} 通过题型区间确定类型: {GetTypeName(sectionType)}");
            return sectionType;
        }

        // 【修复】如果存在题型标题区间但题目不在任何区间内，
        // 这种情况下应该默认为综合题，而不是进行内容特征检测
        // 这样可以避免错误的选择题识别
        if (_questionTypeSections.Count > 0)
        {
            LogInfo($"题目在段落 {questionIndex} 不在任何题型区间内，但文档存在题型标题区间，默认为综合题");
            return QuestionType.Comprehensive;
        }

        // 只有在完全没有题型标题区间的情况下，才使用内容特征检测
        LogInfo($"题目在段落 {questionIndex} 使用内容特征检测（无题型标题区间）");
        return DetectQuestionTypeWithoutSection(paragraphs, questionIndex);
    }

    /// <summary>
    /// 检测题目类型（不使用基于题型标题区间的判断）
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="questionIndex">题目起始段落索引</param>
    /// <returns>题目类型</returns>
    private QuestionType DetectQuestionTypeWithoutSection(List<Paragraph> paragraphs, int questionIndex)
    {
        // 收集题目的上下文(最多查看10个段落)
        StringBuilder contextBuilder = new StringBuilder();
        int maxParas = Math.Min(paragraphs.Count, questionIndex + 10);

        for (int i = questionIndex; i < maxParas; i++)
        {
            string text = paragraphs[i].ToString(SaveFormat.Text).Trim();

            // 如果遇到答案或下一题则停止
            if (text.Contains("【答案】") || (i > questionIndex && Regex.IsMatch(text, @"^\d+[\.．。]")))
            {
                break;
            }

            contextBuilder.Append(" ").Append(text);
        }

        string context = contextBuilder.ToString();

        // 1. 提取题目标题和内容以便更精确判断
        string titleText = paragraphs[questionIndex].ToString(SaveFormat.Text).Trim();

        // 1. 选择题检测
        if (IsChoiceQuestion(context))
        {
            return QuestionType.Choice;
        }

        // 2. 判断题检测
        if (IsTrueFalseQuestion(context))
        {
            return QuestionType.TrueFalse;
        }

        // 3. 填空题检测
        // 检查题目名称明确指出是填空题
        if (titleText.Contains("填空") || context.Contains("填空题"))
        {
            return QuestionType.FillBlank;
        }

        // 检查是否含有填空符号
        if (IsFillBlankQuestion(context))
        {
            if (HasOmmlFormula(paragraphs, questionIndex) && !HasComprehensiveKeywords(context))
            {
                // 含有OMML公式且没有综合题特征的填空题
                return QuestionType.FillBlank;
            }
        }

        // 4. 综合题检测
        // 检查题目名称是否明确指出是解答题、计算题等
        if (titleText.Contains("解答") || titleText.Contains("计算") ||
            titleText.Contains("应用题") || titleText.Contains("证明"))
        {
            return QuestionType.Comprehensive;
        }

        // 检查内容是否具有综合题特征
        if (IsComprehensiveQuestion(context))
        {
            return QuestionType.Comprehensive;
        }

        // 5. 对于带有OMML公式但未明确表示为填空的题目，默认为填空题
        if (HasOmmlFormula(paragraphs, questionIndex) && !HasComprehensiveKeywords(context))
        {
            return QuestionType.FillBlank;
        }

        // 默认为解答题（根据用户要求，无法判断类型的都归类到解答题）
        return QuestionType.Comprehensive;
    }
    
    /// <summary>
    /// 从章节标题检测题型类别
    /// </summary>
    private QuestionType DetectSectionType(string sectionTitle)
    {
        // 按类型匹配章节标题
        if (sectionTitle.Contains("选择") || 
            sectionTitle.Contains("单选") || 
            sectionTitle.Contains("多选"))
        {
            return QuestionType.Choice;
        }
        
        if (sectionTitle.Contains("填空"))
        {
            return QuestionType.FillBlank;
        }
        
        if (sectionTitle.Contains("解答") || 
            sectionTitle.Contains("计算") || 
            sectionTitle.Contains("综合") || 
            sectionTitle.Contains("证明") ||
            sectionTitle.Contains("实验"))
        {
            return QuestionType.Comprehensive;
        }
        
        if (sectionTitle.Contains("判断"))
        {
            return QuestionType.TrueFalse;
        }
        
        return QuestionType.Unknown;
    }
    
    /// <summary>
    /// 判断是否为选择题（更严格的判断逻辑，避免误识别）
    /// </summary>
    private bool IsChoiceQuestion(string text)
    {
        // 更严格的选择题格式特征判断
        // 1. 必须同时包含A和B选项，且格式规范
        bool hasABOptions = (text.Contains("A．") && text.Contains("B．")) ||
                           (text.Contains("A.") && text.Contains("B.")) ||
                           (text.Contains("(A)") && text.Contains("(B)")) ||
                           (text.Contains("（A）") && text.Contains("（B）"));

        // 2. 如果有A和B选项，还应该有C或D选项（大多数选择题至少有3个选项）
        bool hasMoreOptions = text.Contains("C．") || text.Contains("C.") ||
                             text.Contains("(C)") || text.Contains("（C）") ||
                             text.Contains("D．") || text.Contains("D.") ||
                             text.Contains("(D)") || text.Contains("（D）");

        // 3. 明确包含"选择"关键词的情况
        bool hasChoiceKeyword = text.Contains("选择") && hasABOptions;

        // 4. 排除可能的误判情况：如果文本很长且包含解答过程，可能不是选择题
        bool isLikelyNotChoice = text.Length > 500 &&
                                (text.Contains("解：") || text.Contains("解答") ||
                                 text.Contains("证明") || text.Contains("计算过程"));

        // 只有在满足严格条件且不是解答过程的情况下才认为是选择题
        return (hasABOptions && hasMoreOptions && !isLikelyNotChoice) ||
               (hasChoiceKeyword && !isLikelyNotChoice);
    }
    
    /// <summary>
    /// 判断是否为判断题
    /// </summary>
    private bool IsTrueFalseQuestion(string text)
    {
        return (text.Contains("判断") && (text.Contains("正确") || text.Contains("错误"))) ||
               text.Contains("对错") || 
               text.Contains("判断题") ||
               text.Contains("判断对错") ||
               (text.Contains("正确") && text.Contains("错误") && !text.Contains("选择"));
    }
    
    /// <summary>
    /// 判断是否为填空题 - 检查是否包含填空符号
    /// </summary>
    private bool IsFillBlankQuestion(string text)
    {
        // 填空符号特征
        return text.Contains("（）") || 
               text.Contains("(  )") || 
               text.Contains("____") ||
               text.Contains("___") ||
               text.Contains("则     ") ||
               text.Contains("则      ") ||
               text.Contains("则       ") ||
               text.Contains("则        ") ||
               Regex.IsMatch(text, @"\[\s*\]") ||
               Regex.IsMatch(text, @"（\s*）") ||
               Regex.IsMatch(text, @"\( {2,}\)") ||
               Regex.IsMatch(text, @"则 {3,}\.") ||
               text.EndsWith("则        .") ||
               text.Contains("填空");
    }
    
    /// <summary>
    /// 检查段落是否包含OMML公式
    /// </summary>
    private bool HasOmmlFormula(List<Paragraph> paragraphs, int startIndex)
    {
        // 检查当前段落和后续几个段落是否包含OMML公式
        int endIndex = Math.Min(startIndex + 5, paragraphs.Count - 1);
        
        for (int i = startIndex; i <= endIndex; i++)
        {
            Paragraph para = paragraphs[i];
            NodeCollection runs = para.GetChildNodes(NodeType.Run, true);
            
            foreach (Run run in runs)
            {
                // 检查是否包含Office Math标记
                if (run.Text.Contains("\u0001Equation") || 
                    run.Font.Name.Contains("Cambria Math"))
                {
                    return true;
                }
                
                // 检查是否包含特殊字符组合，可能表示公式
                if (Regex.IsMatch(run.Text, @"[√∫∑∏∆∇≈≠≤≥±×÷]"))
                {
                    return true;
                }
            }
            
            // 检查段落格式是否可能包含公式
            NodeCollection childNodes = para.GetChildNodes(NodeType.Any, true);
            foreach (Node node in childNodes)
            {
                if (node.NodeType == NodeType.Shape || 
                    node.NodeType == NodeType.OfficeMath)
                {
                    return true;
                }
            }
            
            // 根据段落内容特征判断
            string paraText = para.ToString(SaveFormat.Text);
            if (paraText.Contains("=") && 
                (paraText.Contains("{") || paraText.Contains("}") || 
                 paraText.Contains("∫") || paraText.Contains("∑")))
            {
                return true;
            }
        }
        
        return false;
    }
    
    /// <summary>
    /// 判断是否为综合题 - 包含解答题、计算题、实验题等
    /// </summary>
    private bool IsComprehensiveQuestion(string text)
    {
        // 综合题通常比较复杂，含有计算、解答等关键词
        return HasComprehensiveKeywords(text) || 
               text.Contains("解：") || 
               text.Contains("证明：") ||
               (text.Contains("计算") && !IsChoiceQuestion(text)) ||
               text.Contains("求解") ||
               (text.Contains("求") && text.Contains("的值")) ||
               (text.Contains("直角坐标系") && !IsFillBlankQuestion(text));
    }
    
    /// <summary>
    /// 判断是否包含综合题关键词
    /// </summary>
    private bool HasComprehensiveKeywords(string text)
    {
        string[] keywords = {
            "证明", "计算", "解答", "求解", "求证", "分析", "实验", 
            "作图", "证明", "推导", "探究", "详解", "函数", "方程", "几何",
            "求导", "积分", "微分", "解方程", "解不等式", "证明定理",
            "画图", "作图", "计算过程", "解题过程"
        };
        
        foreach (string keyword in keywords)
        {
            if (text.Contains(keyword))
            {
                return true;
            }
        }
        
        return false;
    }
    
    /// <summary>
    /// 通过答案内容确认题型
    /// </summary>
    private QuestionType ConfirmTypeByAnswer(string answerText, QuestionInfo question)
    {
        QuestionType detectedType = question.Type;
        
        // 1. 选择题答案特征
        if (Regex.IsMatch(answerText, @"【答案】\s*[A-D]") || 
            Regex.IsMatch(answerText, @"【答案】\s*[A-D][A-D]"))
        {
            return QuestionType.Choice;
        }
        
        // 2. 判断题答案特征
        if (Regex.IsMatch(answerText, @"【答案】\s*(√|×|对|错|正确|错误)"))
        {
            return QuestionType.TrueFalse;
        }
        
        // 3. 填空题特征 - 数学填空题答案通常以数值、表达式或结论形式给出
        string answerContent = ExtractAnswerContent(answerText);
        
        // 3.1 检查是否包含公式末尾的"【1】"标记 - 这通常是填空题
        if (!answerText.Contains("【详解】") && !answerText.Contains("【分析】"))
        {
            return QuestionType.FillBlank;
        }
        
        // 3.2 如果题目已被检测为填空题，但没有综合题特征，则保持填空题判断
        if (detectedType == QuestionType.FillBlank && 
            !answerText.Contains("【详解】") && !answerText.Contains("【分析】") &&
            !answerText.Contains("解答过程") && !answerText.Contains("解："))
        {
            return QuestionType.FillBlank;
        }
        
        // 4. 综合题答案特征 - 有详细分析和解答
        if (answerText.Contains("【分析】") || answerText.Contains("【详解】") ||
            answerText.Contains("解答过程") || answerText.Contains("解：") ||
            answerContent.Length > 50)
        {
            return QuestionType.Comprehensive;
        }
        
        // 5. 如果答案短，且不是选择题和判断题，可能是填空题
        if (answerContent.Length < 30 && !HasComprehensiveKeywords(answerText))
        {
            return QuestionType.FillBlank;
        }
        
        // 6. 检查段落文本中是否包含特定标记
        if (answerText.Contains("计算") || answerText.Contains("证明") || 
            answerText.Contains("解答") || answerText.Contains("变换") ||
            answerText.Contains("整理") || answerText.Contains("如图"))
        {
            return QuestionType.Comprehensive;
        }
        
        // 如果仍然是未知类型，但检测到计算题特征，则设为综合题
        if (detectedType == QuestionType.Unknown && 
            (answerText.Contains("=") || answerText.Contains("计算") || 
             answerText.Contains("方程") || answerText.Contains("函数")))
        {
            return QuestionType.Comprehensive;
        }
        
        // 保留原有判断结果
        return detectedType != QuestionType.Unknown ? detectedType : QuestionType.Comprehensive;
    }
    
    /// <summary>
    /// 提取答案内容部分
    /// </summary>
    private string ExtractAnswerContent(string text)
    {
        Match match = Regex.Match(text, @"【答案】\s*(.+)");
        if (match.Success && match.Groups.Count > 1)
        {
            return match.Groups[1].Value.Trim();
        }
        return text;
    }
    
    /// <summary>
    /// 获取题型名称
    /// </summary>
    private string GetTypeName(QuestionType type)
    {
        switch (type)
        {
            case QuestionType.Choice:
                return "选择题";
            case QuestionType.FillBlank:
                return "填空题";
            case QuestionType.Comprehensive:
                return "综合题";
            case QuestionType.TrueFalse:
                return "判断题";
            default:
                return "未知题型";
        }
    }
    
    /// <summary>
    /// 输出处理统计摘要
    /// </summary>
    private void LogSummary()
    {
        LogInfo("========== 处理统计 ==========");
        LogInfo($"共处理题目: {_totalQuestions} 道");
        LogInfo($"- 选择题: {_typeStatistics[QuestionType.Choice]} 道");
        LogInfo($"- 填空题: {_typeStatistics[QuestionType.FillBlank]} 道");
        LogInfo($"- 综合题: {_typeStatistics[QuestionType.Comprehensive]} 道");
        LogInfo($"- 判断题: {_typeStatistics[QuestionType.TrueFalse]} 道");
        LogInfo($"- 未识别: {_typeStatistics[QuestionType.Unknown]} 道");
        LogInfo("==============================");
    }
    
    /// <summary>
    /// 智能查找有效的答案结束位置，跳过标题、题型标题、换行符、分页符等
    /// 注意：不跳过包含重要内容（图片、公式、表格等）的段落
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="startIndex">开始查找的索引</param>
    /// <param name="answerStartIndex">答案开始位置（不能超过此位置）</param>
    /// <param name="skippedElements">记录跳过的元素</param>
    /// <returns>有效的答案结束位置</returns>
    private int FindValidAnswerEndPosition(List<Paragraph> paragraphs, int startIndex, int answerStartIndex, List<(int index, string type, string content)> skippedElements)
    {
        int currentIndex = startIndex;
        
        while (currentIndex > answerStartIndex)
        {
            Paragraph currentPara = paragraphs[currentIndex];
            string currentText = currentPara.ToString(SaveFormat.Text).Trim();
            
            // 检查是否为空段落（但要排除包含图片、公式等重要内容的段落）
            if (string.IsNullOrWhiteSpace(currentText))
            {
                // 即使文本为空，也要检查是否包含重要内容（如图片、公式等）
                if (!HasImageOrShape(currentPara) && !HasOfficeMath(currentPara) && !HasTable(currentPara) && !HasOtherImportantContent(currentPara))
                {
                    skippedElements.Add((currentIndex, "空段落", currentText));
                    currentIndex--;
                    continue;
                }
                // 如果包含重要内容，继续执行后续检查
            }
            
            // 检查是否包含分节符或分页符
            if (HasSectionBreakOrPageBreak(currentPara))
            {
                skippedElements.Add((currentIndex, "分节符/分页符", currentText));
                currentIndex--;
                continue;
            }
            
            // 【修复】检查图片段落的跳过逻辑
            if (HasImageOrShape(currentPara))
            {
                // 检查是否为纯图片段落（只有图片和空格）
                if (IsPureImageParagraph(currentPara))
                {
                    if (_forceSkipImages)
                    {
                        // 强制跳过模式：跳过所有纯图片段落，不检查上一段落
                        LogInfo($"段落 {currentIndex} 为纯图片段落且强制跳过模式已启用，跳过：'{currentText.Substring(0, Math.Min(30, currentText.Length))}...'");
                        skippedElements.Add((currentIndex, "纯图片段落(强制跳过)", currentText));
                        currentIndex--;
                        continue;
                    }
                    else
                    {
                        // 智能跳过模式：只有当图片段落只含图片且上一个段落是空行时才跳过
                        if (currentIndex - 1 >= 0 && IsEmptyLineParagraph(paragraphs[currentIndex - 1]))
                        {
                            LogInfo($"段落 {currentIndex} 为纯图片段落且上一段为空行，跳过：'{currentText.Substring(0, Math.Min(30, currentText.Length))}...'");
                            skippedElements.Add((currentIndex, "纯图片段落(上段为空行)", currentText));
                            currentIndex--;
                            continue;
                        }
                        else
                        {
                            LogInfo($"段落 {currentIndex} 为纯图片段落但上一段非空行，不跳过：'{currentText.Substring(0, Math.Min(30, currentText.Length))}...'");
                            break;
                        }
                    }
                }
                else
                {
                    LogInfo($"段落 {currentIndex} 包含图片但非纯图片段落，不跳过：'{currentText.Substring(0, Math.Min(30, currentText.Length))}...'");
                    break;
                }
            }
            
            if (HasOfficeMath(currentPara))
            {
                LogInfo($"段落 {currentIndex} 包含公式，不跳过：'{currentText.Substring(0, Math.Min(30, currentText.Length))}...'");
                break; // 找到重要内容，停止跳过
            }
            
            if (HasTable(currentPara))
            {
                LogInfo($"段落 {currentIndex} 包含表格，不跳过：'{currentText.Substring(0, Math.Min(30, currentText.Length))}...'");
                break; // 找到表格，停止跳过，让END标签插入到表格之后
            }
            
            if (HasOtherImportantContent(currentPara))
            {
                LogInfo($"段落 {currentIndex} 包含其他重要内容，不跳过：'{currentText.Substring(0, Math.Min(30, currentText.Length))}...'");
                break; // 找到重要内容，停止跳过
            }
            
            // 检查是否为标题段落（使用完整的标题识别逻辑）
            if (IsTitleParagraph(currentPara, paragraphs, currentIndex))
            {
                skippedElements.Add((currentIndex, "标题段落", currentText));
                currentIndex--;
                continue;
            }
            
            // 检查是否为题型标题（只匹配段落开头的文本，允许开头有空格）
            bool isTargetField = false;
            foreach (string headerText in _questionTypeHeaders)
            {
                if (IsTargetFieldMatch(currentText, headerText))
                {
                    isTargetField = true;
                    skippedElements.Add((currentIndex, "题型标题", currentText));
                    break;
                }
            }
            
            if (isTargetField)
            {
                currentIndex--;
                continue;
            }
            
            // 找到有效的答案结束位置
            break;
        }
        
        // ========================== 新增：额外的标题检查逻辑 ==========================
        // 检查即将插入END标签位置的前面两个段落（上上个和上上上个）是否为标题
        // 如果有任何一个是标题，则继续向前跳过一个段落，并重新按照已有逻辑处理
        currentIndex = PerformExtraTitleCheck(paragraphs, currentIndex, answerStartIndex, skippedElements);
        
        return currentIndex;
    }
    
    /// <summary>
    /// 执行额外的标题检查逻辑
    /// 检查即将插入END标签位置的前面两个段落是否为标题，如果是则继续向前处理
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="currentIndex">当前位置</param>
    /// <param name="answerStartIndex">答案开始位置（不能超过此位置）</param>
    /// <param name="skippedElements">记录跳过的元素</param>
    /// <returns>最终的插入位置</returns>
    private int PerformExtraTitleCheck(List<Paragraph> paragraphs, int currentIndex, int answerStartIndex, List<(int index, string type, string content)> skippedElements)
    {
        // 确保有足够的段落进行检查
        if (currentIndex < 2 || currentIndex - 2 <= answerStartIndex)
        {
            LogInfo("没有足够的段落进行额外标题检查，保持当前位置");
            return currentIndex;
        }
        
        // 检查上上个段落（currentIndex - 1）和上上上个段落（currentIndex - 2）
        bool foundTitleInPreviousParagraphs = false;
        
        // 检查上上个段落（currentIndex - 1）
        if (currentIndex - 1 > answerStartIndex)
        {
            Paragraph prevPrev = paragraphs[currentIndex - 1];
            if (IsTitleParagraph(prevPrev, paragraphs, currentIndex - 1))
            {
                string prevPrevText = prevPrev.ToString(SaveFormat.Text).Trim();
                LogInfo($"发现上上个段落是标题：'{prevPrevText.Substring(0, Math.Min(30, prevPrevText.Length))}...'");
                foundTitleInPreviousParagraphs = true;
            }
        }

        // 检查上上上个段落（currentIndex - 2）
        if (currentIndex - 2 > answerStartIndex)
        {
            Paragraph prevPrevPrev = paragraphs[currentIndex - 2];
            if (IsTitleParagraph(prevPrevPrev, paragraphs, currentIndex - 2))
            {
                string prevPrevPrevText = prevPrevPrev.ToString(SaveFormat.Text).Trim();
                LogInfo($"发现上上上个段落是标题：'{prevPrevPrevText.Substring(0, Math.Min(30, prevPrevPrevText.Length))}...'");
                foundTitleInPreviousParagraphs = true;
            }
        }
        
        // 如果前面的段落中有标题，则跳过上一个段落，继续按照已有逻辑处理
        if (foundTitleInPreviousParagraphs)
        {
            LogInfo("检测到前面有标题段落，跳过上一个段落并继续向前处理");
            
            // 记录跳过的上一个段落
            Paragraph prevPara = paragraphs[currentIndex];
            string prevText = prevPara.ToString(SaveFormat.Text).Trim();
            skippedElements.Add((currentIndex, "由于前方标题而跳过的段落", prevText));
            
            // 从 currentIndex - 1 开始继续按照已有逻辑处理
            int newCurrentIndex = currentIndex - 1;
            
            while (newCurrentIndex > answerStartIndex)
            {
                Paragraph currentPara = paragraphs[newCurrentIndex];
                string currentText = currentPara.ToString(SaveFormat.Text).Trim();
                
                // 检查是否为空段落（但要排除包含图片、公式等重要内容的段落）
                if (string.IsNullOrWhiteSpace(currentText))
                {
                    // 即使文本为空，也要检查是否包含重要内容（如图片、公式等）
                    if (!HasImageOrShape(currentPara) && !HasOfficeMath(currentPara) && !HasTable(currentPara) && !HasOtherImportantContent(currentPara))
                    {
                        skippedElements.Add((newCurrentIndex, "空段落", currentText));
                        newCurrentIndex--;
                        continue;
                    }
                    // 如果包含重要内容，继续执行后续检查
                }
                
                // 检查是否包含分节符或分页符
                if (HasSectionBreakOrPageBreak(currentPara))
                {
                    skippedElements.Add((newCurrentIndex, "分节符/分页符", currentText));
                    newCurrentIndex--;
                    continue;
                }
                
                // 【修复】检查图片段落的跳过逻辑（与主逻辑保持一致）
                if (HasImageOrShape(currentPara))
                {
                    // 检查是否为纯图片段落（只有图片和空格）
                    if (IsPureImageParagraph(currentPara))
                    {
                        if (_forceSkipImages)
                        {
                            // 强制跳过模式：跳过所有纯图片段落，不检查上一段落
                            LogInfo($"额外检查中段落 {newCurrentIndex} 为纯图片段落且强制跳过模式已启用，跳过");
                            skippedElements.Add((newCurrentIndex, "纯图片段落(强制跳过)", currentText));
                            newCurrentIndex--;
                            continue;
                        }
                        else
                        {
                            // 智能跳过模式：检查上一个段落是否为空行（注意：我们是从后往前遍历，所以上一个段落的索引是newCurrentIndex-1）
                            if (newCurrentIndex - 1 >= 0 && IsEmptyLineParagraph(paragraphs[newCurrentIndex - 1]))
                            {
                                LogInfo($"额外检查中段落 {newCurrentIndex} 为纯图片段落且上一段为空行，跳过");
                                skippedElements.Add((newCurrentIndex, "纯图片段落(上段为空行)", currentText));
                                newCurrentIndex--;
                                continue;
                            }
                            else
                            {
                                LogInfo($"额外检查中段落 {newCurrentIndex} 为纯图片段落但上一段非空行，不跳过");
                                break;
                            }
                        }
                    }
                    else
                    {
                        LogInfo($"额外检查中段落 {newCurrentIndex} 包含图片但非纯图片段落，不跳过");
                        break;
                    }
                }
                
                if (HasOfficeMath(currentPara))
                {
                    LogInfo($"额外检查中段落 {newCurrentIndex} 包含公式，不跳过");
                    break;
                }
                
                if (HasTable(currentPara))
                {
                    LogInfo($"额外检查中段落 {newCurrentIndex} 包含表格，不跳过");
                    break;
                }
                
                if (HasOtherImportantContent(currentPara))
                {
                    LogInfo($"额外检查中段落 {newCurrentIndex} 包含其他重要内容，不跳过");
                    break;
                }
                
                // 检查是否为标题段落（使用完整的标题识别逻辑）
                if (IsTitleParagraph(currentPara, paragraphs, newCurrentIndex))
                {
                    skippedElements.Add((newCurrentIndex, "标题段落", currentText));
                    newCurrentIndex--;
                    continue;
                }
                
                // 检查是否为题型标题
                bool isTargetField = false;
                foreach (string headerText in _questionTypeHeaders)
                {
                    if (IsTargetFieldMatch(currentText, headerText))
                    {
                        isTargetField = true;
                        skippedElements.Add((newCurrentIndex, "题型标题", currentText));
                        break;
                    }
                }
                
                if (isTargetField)
                {
                    newCurrentIndex--;
                    continue;
                }
                
                // 找到有效的答案结束位置
                break;
            }
            
            LogInfo($"额外标题检查后，最终位置从 {currentIndex} 调整为 {newCurrentIndex}");
            return newCurrentIndex;
        }
        else
        {
            LogInfo("前面的段落中没有标题，保持原有位置");
            return currentIndex;
        }
    }
    
    /// <summary>
    /// 检查段落文本是否匹配目标字段（只匹配开头，允许开头有各种编码的空格）
    /// </summary>
    /// <param name="paragraphText">段落文本</param>
    /// <param name="targetField">目标字段文本</param>
    /// <returns>是否匹配</returns>
    private bool IsTargetFieldMatch(string paragraphText, string targetField)
    {
        if (string.IsNullOrEmpty(paragraphText) || string.IsNullOrEmpty(targetField))
            return false;
        
        // 移除段落开头的所有空白字符（包括各种编码的空格、制表符等）
        string trimmedParagraphText = paragraphText.TrimStart();
        
        // 检查是否以目标字段开头（忽略大小写）
        if (trimmedParagraphText.StartsWith(targetField, StringComparison.OrdinalIgnoreCase))
        {
            LogInfo($"段落开头匹配目标字段：'{paragraphText.Substring(0, Math.Min(30, paragraphText.Length))}...' 匹配 '{targetField}'");
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// 检查段落是否包含分节符
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否包含分节符</returns>
    private bool HasSectionBreak(Paragraph para)
    {
        // 检查段落格式的分节符设置
        if (para.ParagraphFormat.PageBreakBefore)
        {
            LogInfo($"检测到段落分节符设置：PageBreakBefore = true");
            return true;
        }
        
        // 检查段落中是否包含分节符
        foreach (Node child in para.GetChildNodes(NodeType.Any, true))
        {
            if (child is Run run)
            {
                if (run.Text.Contains('\x000C') || run.Text.Contains('\x0014')) // 分节符字符
                {
                    LogInfo($"检测到分节符字符");
                    return true;
                }
            }
        }
        
        return false;
    }

    /// <summary>
    /// 检查段落是否包含分节符或分页符
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否包含分节符或分页符</returns>
    private bool HasSectionBreakOrPageBreak(Paragraph para)
    {
        // 注意：PageBreakBefore属性在主算法中单独处理，这里不重复检查
            
        string text = para.ToString(SaveFormat.Text);
        string paraText = text.Trim();
        
        // 2. 检查是否包含分页符字符
        if (text.Contains("\f"))
        {
            LogInfo($"检测到分页符字符 \\f：{paraText.Substring(0, Math.Min(20, paraText.Length))}...");
            return true;
        }
            
        // 3. 检查是否包含分节符字符 (\x0E)
        if (text.Contains("\x0E"))
        {
            LogInfo($"检测到分节符字符 \\x0E：{paraText.Substring(0, Math.Min(20, paraText.Length))}...");
            return true;
        }
        
        // 4. 检查段落中的所有Run节点
        foreach (Node node in para.GetChildNodes(NodeType.Run, true))
        {
            if (node is Run run && run.Text != null)
            {
                // 检查分页符
                if (run.Text.Contains("\f"))
                {
                    LogInfo($"检测到Run中的分页符字符 \\f：{run.Text}");
                    return true;
                }
                // 检查分节符
                if (run.Text.Contains("\x0E"))
                {
                    LogInfo($"检测到Run中的分节符字符 \\x0E：{run.Text}");
                    return true;
                }
            }
        }
        
        // 5. 检查是否包含其他特殊Break节点（包括FieldStart等）
        NodeCollection allChildren = para.GetChildNodes(NodeType.Any, true);
        foreach (Node child in allChildren)
        {
            // 检查是否有特殊的分页符节点
            if (child.NodeType == NodeType.SpecialChar)
            {
                LogInfo($"检测到特殊字符节点：{child.GetText()}");
                string specialText = child.GetText();
                if (specialText.Contains("\f") || specialText.Contains("\x0E"))
                {
                    LogInfo($"特殊字符节点包含分页符/分节符");
                    return true;
                }
            }
        }
        
        // 6. 检查段落是否在分节符之前（检查段落所在的Section是否是新的分节）
        Section currentSection = para.ParentSection;
        if (currentSection != null)
        {
            // 检查是否是该Section的第一个段落，且有多个Section
            Document doc = (Document)currentSection.Document;
            if (doc.Sections.Count > 1 && currentSection.Body.FirstParagraph == para)
            {
                // 如果不是第一个Section的第一个段落，则说明此处有分节符
                int sectionIndex = doc.Sections.IndexOf(currentSection);
                if (sectionIndex > 0)
                {
                    LogInfo($"检测到Section边界分节符，Section索引：{sectionIndex}");
                    return true;
                }
            }
        }
        
        // 7. 额外检查：查看段落的下一个段落是否在新页面上
        Paragraph nextPara = para.NextSibling as Paragraph;
        if (nextPara != null && nextPara.ParagraphFormat.PageBreakBefore)
        {
            LogInfo($"检测到下一个段落有分页符：{nextPara.ToString(SaveFormat.Text).Trim().Substring(0, Math.Min(20, nextPara.ToString(SaveFormat.Text).Trim().Length))}...");
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// 检查段落是否为标题段落（完全基于NormalizeTitleStep的标题识别逻辑）
    /// 标题特征：文档最前面的几行，居中对齐，以空行结束
    /// 扩展：每个section开头的连续居中段落也被视为标题
    /// 扩展：每个页面开头（分页符后）的连续居中段落也被视为标题
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <param name="paragraphs">段落列表（用于上下文分析）</param>
    /// <param name="paraIndex">段落在列表中的索引</param>
    /// <returns>是否为标题段落</returns>
    private bool IsTitleParagraph(Paragraph para, List<Paragraph> paragraphs, int paraIndex)
    {
        // 检查是否在页眉页脚中
        if (para.GetAncestor(typeof(HeaderFooter)) != null)
            return false;

        // 检查是否为居中对齐
        if (para.ParagraphFormat.Alignment != ParagraphAlignment.Center)
            return false;

        // 1. 检查是否在文档开头的标题区域
        if (IsInDocumentStartTitleRegion(paragraphs, paraIndex))
            return true;

        // 2. 检查是否在section开头的标题区域
        if (IsInSectionStartTitleRegion(para, paragraphs, paraIndex))
            return true;

        // 3. 检查是否在分页符后的标题区域
        if (IsInPageBreakTitleRegion(paragraphs, paraIndex))
            return true;

        return false;
    }

    /// <summary>
    /// 检查段落是否为标题段落（简化版本，用于向后兼容）
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否为标题段落</returns>
    private bool IsTitleParagraph(Paragraph para)
    {
        // 检查是否在页眉页脚中
        if (para.GetAncestor(typeof(HeaderFooter)) != null)
            return false;

        // 检查是否为居中对齐
        if (para.ParagraphFormat.Alignment != ParagraphAlignment.Center)
            return false;

        // 简化判断：居中对齐的段落都视为标题
        return true;
    }

    /// <summary>
    /// 检查段落是否在文档开头的标题区域
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="paraIndex">段落索引</param>
    /// <returns>是否在文档开头的标题区域</returns>
    private bool IsInDocumentStartTitleRegion(List<Paragraph> paragraphs, int paraIndex)
    {
        // 从文档开头开始检查连续的居中段落
        for (int i = 0; i <= paraIndex && i < paragraphs.Count; i++)
        {
            Paragraph para = paragraphs[i];

            // 如果当前段落不是居中对齐，则标题区域结束
            if (para.ParagraphFormat.Alignment != ParagraphAlignment.Center)
            {
                return false; // 当前段落不在标题区域内
            }

            // 如果是空的居中段落，认为标题区域结束
            if (string.IsNullOrWhiteSpace(para.GetText().Trim()))
            {
                return i >= paraIndex; // 如果当前检查的段落索引已经到达或超过目标段落，则目标段落在标题区域内
            }
        }

        // 如果所有段落都是非空的居中段落，则都在标题区域内
        return true;
    }

    /// <summary>
    /// 检查段落是否在section开头的标题区域
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="paraIndex">段落索引</param>
    /// <returns>是否在section开头的标题区域</returns>
    private bool IsInSectionStartTitleRegion(Paragraph para, List<Paragraph> paragraphs, int paraIndex)
    {
        // 检查当前段落是否是section的第一个段落
        if (IsFirstParagraphInSection(para))
        {
            // 从这个段落开始检查连续的居中段落
            return IsInConsecutiveCenteredRegion(paragraphs, paraIndex);
        }

        // 检查前面的段落中是否有section的第一个段落
        for (int i = paraIndex - 1; i >= 0; i--)
        {
            Paragraph prevPara = paragraphs[i];

            // 如果找到section的第一个段落
            if (IsFirstParagraphInSection(prevPara))
            {
                // 检查从那个段落到当前段落是否都是连续的居中段落
                return IsConsecutiveCenteredFromTo(paragraphs, i, paraIndex);
            }

            // 如果遇到非居中段落，则停止查找
            if (prevPara.ParagraphFormat.Alignment != ParagraphAlignment.Center)
            {
                break;
            }
        }

        return false;
    }

    /// <summary>
    /// 检查段落是否在分页符后的标题区域
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="paraIndex">段落索引</param>
    /// <returns>是否在分页符后的标题区域</returns>
    private bool IsInPageBreakTitleRegion(List<Paragraph> paragraphs, int paraIndex)
    {
        // 检查前面的段落中是否有分页符
        for (int i = paraIndex - 1; i >= 0; i--)
        {
            Paragraph prevPara = paragraphs[i];

            // 如果找到分页符
            if (HasPageBreakBefore(paragraphs[i + 1]) || HasPageBreakAfter(prevPara))
            {
                // 检查从分页符后到当前段落是否都是连续的居中段落
                return IsConsecutiveCenteredFromTo(paragraphs, i + 1, paraIndex);
            }

            // 如果遇到非居中段落，则停止查找
            if (prevPara.ParagraphFormat.Alignment != ParagraphAlignment.Center)
            {
                break;
            }
        }

        return false;
    }

    /// <summary>
    /// 检查从指定位置开始是否为连续的居中段落区域
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="startIndex">开始索引</param>
    /// <returns>是否为连续的居中段落区域</returns>
    private bool IsInConsecutiveCenteredRegion(List<Paragraph> paragraphs, int startIndex)
    {
        for (int i = startIndex; i < paragraphs.Count; i++)
        {
            Paragraph para = paragraphs[i];

            // 如果不是居中对齐，则区域结束
            if (para.ParagraphFormat.Alignment != ParagraphAlignment.Center)
            {
                return false;
            }

            // 如果是空的居中段落，认为标题区域结束，但当前段落仍在区域内
            if (string.IsNullOrWhiteSpace(para.GetText().Trim()))
            {
                return true;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查从起始位置到结束位置是否都是连续的居中段落
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="startIndex">起始索引</param>
    /// <param name="endIndex">结束索引</param>
    /// <returns>是否都是连续的居中段落</returns>
    private bool IsConsecutiveCenteredFromTo(List<Paragraph> paragraphs, int startIndex, int endIndex)
    {
        for (int i = startIndex; i <= endIndex && i < paragraphs.Count; i++)
        {
            Paragraph para = paragraphs[i];

            // 如果不是居中对齐，则不是连续的居中段落
            if (para.ParagraphFormat.Alignment != ParagraphAlignment.Center)
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查段落是否是section的第一个段落
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否是section的第一个段落</returns>
    private bool IsFirstParagraphInSection(Paragraph para)
    {
        // 获取段落所在的section
        Section section = para.ParentSection;
        if (section == null) return false;

        // 获取section的第一个段落
        Body body = section.Body;
        if (body == null || body.FirstParagraph == null) return false;

        // 检查当前段落是否是section的第一个段落
        return para == body.FirstParagraph;
    }

    /// <summary>
    /// 检查段落是否有分页符前缀
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否有分页符前缀</returns>
    private bool HasPageBreakBefore(Paragraph para)
    {
        return para.ParagraphFormat.PageBreakBefore;
    }

    /// <summary>
    /// 检查段落是否有分页符后缀
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否有分页符后缀</returns>
    private bool HasPageBreakAfter(Paragraph para)
    {
        // 检查段落中是否包含分页符
        string text = para.ToString(SaveFormat.Text);

        // 检查是否有分页符字符
        if (text.Contains("\f"))
            return true;

        // 检查段落中的所有Run
        foreach (Node node in para.GetChildNodes(NodeType.Run, true))
        {
            if (node is Run run && run.Text != null && run.Text.Contains("\f"))
                return true;
        }

        return false;
    }

    /// <summary>
    /// 检查段落是否包含图片或形状（参考SetEmptyParagraphsStep的实现）
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否包含图片或形状</returns>
    private bool HasImageOrShape(Paragraph para)
    {
        // 检查Shape节点（包含图片和其他形状）
        foreach (Node node in para.GetChildNodes(NodeType.Shape, true))
        {
            if (node is Shape shape && shape.ImageData.HasImage)
                return true;
        }

        // 检查GroupShape节点（组合形状）
        if (para.GetChildNodes(NodeType.GroupShape, true).Count > 0)
            return true;

        // 检查是否有非图片的形状
        if (para.GetChildNodes(NodeType.Shape, true).Count > 0)
            return true;

        return false;
    }
    
    /// <summary>
    /// 检查段落是否包含公式
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否包含公式</returns>
    private bool HasOfficeMath(Paragraph para)
    {
        NodeCollection mathNodes = para.GetChildNodes(NodeType.OfficeMath, true);
        return mathNodes.Count > 0;
    }
    
    /// <summary>
    /// 检查段落是否包含表格
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否包含表格</returns>
    private bool HasTable(Paragraph para)
    {
        // 检查是否在表格内
        Node parent = para.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        
        // 检查是否包含表格节点
        NodeCollection tableNodes = para.GetChildNodes(NodeType.Table, true);
        return tableNodes.Count > 0;
    }
    
    /// <summary>
    /// 检查段落是否包含书签或超链接
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否包含书签或超链接</returns>
    private bool HasBookmarkOrHyperlink(Paragraph para)
    {
        NodeCollection allNodes = para.GetChildNodes(NodeType.Any, true);
        foreach (Node node in allNodes)
        {
            if (node.NodeType == NodeType.BookmarkStart || 
                node.NodeType == NodeType.BookmarkEnd ||
                node.NodeType == NodeType.FieldStart ||
                node.NodeType == NodeType.FieldEnd)
            {
                return true;
            }
        }
        return false;
    }
    
    /// <summary>
    /// 检查段落是否包含其他重要非文本内容
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否包含其他重要内容</returns>
    private bool HasOtherImportantContent(Paragraph para)
    {
        NodeCollection allNodes = para.GetChildNodes(NodeType.Any, false);
        foreach (Node node in allNodes)
        {
            // 如果包含除了Run以外的其他节点类型，可能是重要内容
            if (node.NodeType != NodeType.Run)
            {
                LogInfo($"发现非Run节点类型：{node.NodeType}");
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 检查段落是否为纯图片段落（只包含图片和空格）
    /// 支持各种Unicode空格字符的识别
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否为纯图片段落</returns>
    private bool IsPureImageParagraph(Paragraph para)
    {
        bool hasImage = false;
        
        // 检查所有子节点
        foreach (Node node in para.GetChildNodes(NodeType.Any, true))
        {
            if (node.NodeType == NodeType.Shape)
            {
                hasImage = true;
            }
            else if (node.NodeType == NodeType.GroupShape)
            {
                hasImage = true;
            }
            else if (node.NodeType == NodeType.Run)
            {
                Run run = (Run)node;
                string text = run.Text ?? "";
                
                // 检查Run中的文本是否只包含空格（支持各种Unicode空格）
                if (!string.IsNullOrEmpty(text) && !IsOnlyWhitespace(text))
                {
                    // 如果包含非空格字符，则不是纯图片段落
                    return false;
                }
            }
            else if (node.NodeType != NodeType.Paragraph)
            {
                // 如果包含其他类型的节点（除了段落本身），可能不是纯图片
                // 但某些节点类型（如BookmarkStart/End）可以忽略
                if (!IsIgnorableNodeType(node.NodeType))
                {
                    return false;
                }
            }
        }
        
        return hasImage; // 只有包含图片且没有其他重要内容才是纯图片段落
    }

    /// <summary>
    /// 检查段落是否为空行段落（只包含空格或完全为空）
    /// 支持各种Unicode空格字符的识别
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否为空行段落</returns>
    private bool IsEmptyLineParagraph(Paragraph para)
    {
        string text = para.ToString(SaveFormat.Text).Trim();
        return IsOnlyWhitespace(text);
    }

    /// <summary>
    /// 检查字符串是否只包含空白字符（支持各种Unicode空格）
    /// </summary>
    /// <param name="text">要检查的文本</param>
    /// <returns>是否只包含空白字符</returns>
    private bool IsOnlyWhitespace(string text)
    {
        if (string.IsNullOrEmpty(text))
            return true;
            
        // 使用正则表达式检查是否只包含空白字符
        // \s 匹配所有Unicode空白字符，包括：
        // - 普通空格 (U+0020)
        // - 制表符 (U+0009)
        // - 换行符 (U+000A, U+000D)
        // - 全角空格 (U+3000)
        // - 其他Unicode空格字符
        return Regex.IsMatch(text, @"^\s*$", RegexOptions.Compiled);
    }

    /// <summary>
    /// 检查节点类型是否可以在纯图片段落中忽略
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>是否可以忽略</returns>
    private bool IsIgnorableNodeType(NodeType nodeType)
    {
        return nodeType == NodeType.BookmarkStart ||
               nodeType == NodeType.BookmarkEnd ||
               nodeType == NodeType.CommentRangeStart ||
               nodeType == NodeType.CommentRangeEnd ||
               nodeType == NodeType.MoveFromRangeStart ||
               nodeType == NodeType.MoveFromRangeEnd ||
               nodeType == NodeType.MoveToRangeStart ||
               nodeType == NodeType.MoveToRangeEnd;
    }
    
    /// <summary>
    /// 确保END标签后如果紧跟图片段落，它们之间有一个空行
    /// 扫描END标签后的段落，找到第一个图片段落，确保它们之间有且仅有一个空行
    /// </summary>
    /// <param name="endTagPara">END标签段落</param>
    private void EnsureEmptyLineBeforeImageParagraph(Paragraph endTagPara)
    {
        try
        {
            LogInfo("开始检查END标签后的图片段落空行情况");
            
            // 扫描END标签后的段落，寻找第一个图片段落
            Paragraph currentPara = endTagPara.NextSibling as Paragraph;
            Paragraph firstImagePara = null;
            List<Paragraph> betweenParagraphs = new List<Paragraph>(); // END标签和图片段落之间的段落
            
            int scanCount = 0;
            const int maxScanCount = 10; // 最多扫描10个段落，避免无限循环
            
            while (currentPara != null && scanCount < maxScanCount)
            {
                scanCount++;
                string currentText = currentPara.ToString(SaveFormat.Text).Trim();
                
                LogInfo($"扫描段落 {scanCount}: '{currentText.Substring(0, Math.Min(50, currentText.Length))}...'");
                
                // 检查是否为图片段落
                if (HasImageOrShape(currentPara))
                {
                    firstImagePara = currentPara;
                    LogInfo($"找到第一个图片段落，位于第 {scanCount} 个位置");
                    break;
                }
                
                // 检查是否为题目开始（停止扫描）
                if (Regex.IsMatch(currentText, @"^\s*\d+[\．]\s*"))
                {
                    LogInfo($"遇到下一题开始，停止扫描：'{currentText.Substring(0, Math.Min(30, currentText.Length))}...'");
                    break;
                }
                
                // 记录中间的段落
                betweenParagraphs.Add(currentPara);
                currentPara = currentPara.NextSibling as Paragraph;
            }
            
            if (firstImagePara == null)
            {
                LogInfo("END标签后未找到图片段落，无需处理");
                return;
            }
            
            LogInfo($"END标签和图片段落之间有 {betweenParagraphs.Count} 个段落");
            
            // 检查END标签和图片段落之间的空行情况
            bool hasEmptyLine = false;
            int emptyLineCount = 0;
            
            foreach (var para in betweenParagraphs)
            {
                string text = para.ToString(SaveFormat.Text).Trim();
                if (IsOnlyWhitespace(text) && !HasImageOrShape(para) && 
                    !HasOfficeMath(para) && !HasTable(para) && !HasOtherImportantContent(para))
                {
                    hasEmptyLine = true;
                    emptyLineCount++;
                    LogInfo($"发现空行段落，当前空行数: {emptyLineCount}");
                }
            }
            
            LogInfo($"空行检查结果: hasEmptyLine={hasEmptyLine}, emptyLineCount={emptyLineCount}");
            
            if (!hasEmptyLine)
            {
                // 没有空行，需要插入一个空行
                LogInfo("END标签和图片段落之间没有空行，插入空行");
                InsertEmptyLineBetweenParagraphs(endTagPara, firstImagePara);
            }
            else if (emptyLineCount == 1)
            {
                // 已有一个空行，符合要求
                LogInfo("END标签和图片段落之间已有一个空行，符合要求");
            }
            else if (emptyLineCount > 1)
            {
                // 有多个空行，保留一个，删除多余的
                LogInfo($"END标签和图片段落之间有 {emptyLineCount} 个空行，保留一个，删除多余的");
                CleanupExcessiveEmptyLinesBetweenParagraphs(endTagPara, firstImagePara, betweenParagraphs);
            }
        }
        catch (Exception ex)
        {
            LogError($"确保END标签和图片段落之间空行时出错: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 在两个段落之间插入空行
    /// </summary>
    /// <param name="beforePara">前一个段落</param>
    /// <param name="afterPara">后一个段落</param>
    private void InsertEmptyLineBetweenParagraphs(Paragraph beforePara, Paragraph afterPara)
    {
        try
        {
            // 创建空行段落
            Paragraph newEmptyPara = (Paragraph)beforePara.Clone(true);
            
            // 清空段落内容，保留格式
            NodeCollection runs = newEmptyPara.GetChildNodes(NodeType.Run, true);
            foreach (Run run in runs.OfType<Run>())
            {
                run.Text = string.Empty;
            }
            
            // 清除段落底纹和边框
            ParagraphFormat format = newEmptyPara.ParagraphFormat;
            format.Shading.BackgroundPatternColor = Color.Empty;
            format.Shading.Texture = TextureIndex.TextureNone;
            format.Borders.ClearFormatting();
            
            // 在前一个段落后插入空行
            beforePara.ParentNode.InsertAfter(newEmptyPara, beforePara);
            LogInfo("已插入空行分隔END标签和图片段落");
        }
        catch (Exception ex)
        {
            LogError($"插入空行时出错: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 清理两个段落之间的多余空行，保留一个
    /// </summary>
    /// <param name="beforePara">前一个段落</param>
    /// <param name="afterPara">后一个段落</param>
    /// <param name="betweenParagraphs">中间的段落列表</param>
    private void CleanupExcessiveEmptyLinesBetweenParagraphs(Paragraph beforePara, Paragraph afterPara, List<Paragraph> betweenParagraphs)
    {
        try
        {
            bool hasKeptOneEmptyLine = false;
            int removedCount = 0;
            
            foreach (var para in betweenParagraphs)
            {
                string text = para.ToString(SaveFormat.Text).Trim();
                if (IsOnlyWhitespace(text) && !HasImageOrShape(para) && 
                    !HasOfficeMath(para) && !HasTable(para) && !HasOtherImportantContent(para))
                {
                    if (!hasKeptOneEmptyLine)
                    {
                        // 保留第一个空行
                        hasKeptOneEmptyLine = true;
                        LogInfo("保留第一个空行");
                    }
                    else
                    {
                        // 删除多余的空行
                        if (para.ParentNode != null)
                        {
                            para.Remove();
                            removedCount++;
                            LogInfo("删除多余的空行");
                        }
                    }
                }
            }
            
            LogInfo($"清理完成，删除了 {removedCount} 个多余空行");
        }
        catch (Exception ex)
        {
            LogError($"清理多余空行时出错: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 处理表格结尾插入逻辑 - 在表格后插入空段落用于放置END标签
    /// </summary>
    /// <param name="tablePara">包含表格的段落</param>
    /// <returns>新创建的空段落，用于插入END标签</returns>
    private Paragraph HandleTableEndInsertion(Paragraph tablePara)
    {
        // 找到表格节点
        Table table = null;
        Node parent = tablePara.ParentNode;
        while (parent != null)
        {
            if (parent is Table)
            {
                table = (Table)parent;
                break;
            }
            parent = parent.ParentNode;
        }
        
        if (table == null)
        {
            LogInfo("段落声称包含表格但未找到表格节点，按普通段落处理");
            return tablePara;
        }
        
        // 在表格后创建空段落用于插入END标签
        Document doc = (Document)table.Document;
        Paragraph emptyPara = new Paragraph(doc);
        emptyPara.AppendChild(new Run(doc, ""));
        
        // 将空段落插入到表格后面
        table.ParentNode.InsertAfter(emptyPara, table);
        
        LogInfo("在表格后插入了空段落，用于放置END标签");
        
        // 返回新创建的空段落，END标签将插入到这个段落中
        return emptyPara;
    }
    
    /// <summary>
    /// 检查END标签是否在文档末尾，如果是则清理末尾多余空行，只保留一个空行
    /// 判断标准：END标签后面要么什么都没有，要么最多只有若干空行
    /// 处理策略：从文档最后一个段落开始向前删除空行，直到END标签后只剩一个空行
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <param name="endTagPara">END标签段落</param>
    private void CleanupTrailingEmptyLinesIfAtDocumentEnd(Document doc, Paragraph endTagPara)
    {
        LogInfo("开始检查END标签是否在文档末尾并清理多余空行");
        
        // 重新获取文档中所有段落（包括表格内段落）
        var allParagraphs = new List<Paragraph>();
        foreach (Section section in doc.Sections)
        {
            foreach (Node node in section.Body.GetChildNodes(NodeType.Paragraph, true))
            {
                if (node is Paragraph para)
                {
                    // 排除页眉页脚段落
                    if (para.ParentNode != null && 
                        para.ParentNode.NodeType != NodeType.HeaderFooter)
                    {
                        allParagraphs.Add(para);
                    }
                }
            }
        }
        
        LogInfo($"文档共有 {allParagraphs.Count} 个段落（包括表格内段落）");
        
        // 找到END标签段落在完整列表中的位置
        int endTagIndex = -1;
        string endTagText = endTagPara.ToString(SaveFormat.Text).Trim();
        
        for (int i = 0; i < allParagraphs.Count; i++)
        {
            if (allParagraphs[i] == endTagPara)
            {
                endTagIndex = i;
                LogInfo($"找到END标签段落在位置 {i}: '{endTagText}'");
                break;
            }
        }
        
        if (endTagIndex == -1)
        {
            LogWarning("未找到END标签段落在完整段落列表中的位置，跳过文档末尾检查");
            return;
        }
        
        // 检查END标签后是否只有空行（从END标签后开始检查到文档末尾）
        bool isAtDocumentEnd = true;
        List<int> trailingEmptyLineIndices = new List<int>();
        
        for (int i = endTagIndex + 1; i < allParagraphs.Count; i++)
        {
            Paragraph para = allParagraphs[i];
            string paraText = para.ToString(SaveFormat.Text).Trim();
            
            LogInfo($"检查END标签后段落 {i}: '{paraText.Substring(0, Math.Min(50, paraText.Length))}...'");
            
            // 检查是否为纯空行
            if (string.IsNullOrWhiteSpace(paraText))
            {
                // 进一步检查是否包含任何实际内容
                bool hasImage = HasImageOrShape(para);
                bool hasMath = HasOfficeMath(para);
                bool hasTable = HasTable(para);
                bool hasOther = HasOtherImportantContent(para);
                bool hasPageBreak = HasSectionBreakOrPageBreak(para);
                
                LogInfo($"段落 {i} 内容检查：hasImage={hasImage}, hasMath={hasMath}, hasTable={hasTable}, hasOther={hasOther}, hasPageBreak={hasPageBreak}");
                
                if (hasImage || hasMath || hasTable || hasOther || hasPageBreak)
                {
                    // 包含实际内容，不是纯空行，END标签不在文档末尾
                    LogInfo($"段落 {i} 包含实际内容，END标签不在文档末尾");
                    isAtDocumentEnd = false;
                    break;
                }
                else
                {
                    // 这是真正的空行
                    trailingEmptyLineIndices.Add(i);
                    LogInfo($"发现末尾空行：段落 {i}");
                }
            }
            else
            {
                // 遇到有文本内容的段落，END标签不在文档末尾
                LogInfo($"遇到有文本内容的段落 {i}: '{paraText.Substring(0, Math.Min(30, paraText.Length))}...'，END标签不在文档末尾");
                isAtDocumentEnd = false;
                break;
            }
        }
        
        if (!isAtDocumentEnd)
        {
            LogInfo("END标签不在文档末尾，跳过末尾空行清理");
            return;
        }
        
        LogInfo($"END标签在文档末尾，发现 {trailingEmptyLineIndices.Count} 个末尾空行");
        
        // 执行从文档末尾开始的空行清理，只保留一个空行
        if (trailingEmptyLineIndices.Count > 1)
        {
            LogInfo($"开始从文档末尾删除多余空行，保留1个，删除 {trailingEmptyLineIndices.Count - 1} 个");
            
            // 从最后一个空行开始删除，保留第一个空行
            // 注意：从后往前删除，避免索引变化影响
            int removedCount = 0;
            for (int j = trailingEmptyLineIndices.Count - 1; j >= 1; j--)
            {
                int indexToRemove = trailingEmptyLineIndices[j];
                if (indexToRemove < allParagraphs.Count && allParagraphs[indexToRemove].ParentNode != null)
                {
                    LogInfo($"删除文档末尾多余空行：段落 {indexToRemove}");
                    allParagraphs[indexToRemove].Remove();
                    removedCount++;
                }
            }
            
            LogInfo($"文档末尾空行清理完成，删除了 {removedCount} 个多余空行，保留1个空行");
        }
        else if (trailingEmptyLineIndices.Count == 1)
        {
            LogInfo("文档末尾有1个空行，符合要求，无需清理");
        }
        else if (trailingEmptyLineIndices.Count == 0)
        {
            LogInfo("文档末尾没有空行，这可能是因为已经在前面的步骤中插入了空行");
        }
    }
    
    // 日志方法
    private void LogInfo(string message) => _logs.Add($"[INFO] {message}");
    private void LogWarning(string message) => _logs.Add($"[WARN] {message}");
    private void LogError(string message) => _logs.Add($"[ERROR] {message}");
    
    /// <summary>
    /// 获取处理日志
    /// </summary>
    public List<string> GetLogs() => _logs;
    
    /// <summary>
    /// 内部类：题型区间信息
    /// </summary>
    private class QuestionTypeSection
    {
        public int StartIndex { get; set; }     // 区间开始段落索引
        public int EndIndex { get; set; }       // 区间结束段落索引
        public QuestionType Type { get; set; }  // 题型
        public string Title { get; set; } = string.Empty;       // 题型标题
    }
    
    /// <summary>
    /// 内部类：题目信息
    /// </summary>
    private class QuestionInfo
    {
        public int QuestionStartIndex { get; set; }  // 题目开始的段落索引
        public int AnswerStartIndex { get; set; }    // 答案开始的段落索引
        public int AnswerEndIndex { get; set; }      // 答案结束的段落索引
        public QuestionType Type { get; set; }       // 题目类型
        public bool IsProcessed { get; set; } = false; // 是否已被处理（避免重复插入标签）
    }
}