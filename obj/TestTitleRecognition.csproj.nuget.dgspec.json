{"format": 1, "restore": {"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/TestTitleRecognition.csproj": {}}, "projects": {"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/TestTitleRecognition.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/TestTitleRecognition.csproj", "projectName": "TestTitleRecognition", "projectPath": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/TestTitleRecognition.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/WordProcessorLib/WordProcessorLib.csproj": {"projectPath": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/WordProcessorLib/WordProcessorLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/WordProcessorLib/WordProcessorLib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/WordProcessorLib/WordProcessorLib.csproj", "projectName": "WordProcessorLib", "projectPath": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/WordProcessorLib/WordProcessorLib.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/WordBatchProcessor/WordProcessorLib/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"ShellProgressBar": {"target": "Package", "version": "[5.2.0, )"}, "Spectre.Console": {"target": "Package", "version": "[0.50.1-preview.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}